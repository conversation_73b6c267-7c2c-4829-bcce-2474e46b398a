'use client';

import { useSearchParams, useRouter } from 'next/navigation';
import { AlertCircle, ArrowLeft, Mail } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { authWithToast } from '@/lib/utils/auth';
import { useState, Suspense } from 'react';

function AuthErrorContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const message =
    searchParams.get('message') || 'An authentication error occurred';
  const [isResending, setIsResending] = useState(false);

  const handleGoBack = () => {
    router.push('/login');
  };

  const handleResendVerification = async () => {
    const email = prompt(
      'Please enter your email address to resend verification:'
    );
    if (!email) return;

    setIsResending(true);
    try {
      await authWithToast.forgotPassword(email);
    } catch (error) {
      console.error('Error resending verification:', error);
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className='min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8'>
      <div className='max-w-md w-full space-y-8'>
        <div className='border border-red-200 bg-red-50 rounded-lg shadow-sm'>
          <header className='text-center p-6'>
            <div className='mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4'>
              <AlertCircle className='h-8 w-8 text-red-600' />
            </div>
            <h1 className='text-2xl font-bold text-red-900'>
              Verification Failed
            </h1>
            <p className='text-red-700 mt-2'>
              We couldn&apos;t verify your email address.
            </p>
          </header>

          <div className='space-y-6 p-6 pt-0'>
            <div className='p-3 bg-white rounded-lg border border-red-200'>
              <p className='text-sm text-gray-900 font-medium mb-1'>
                Error Details:
              </p>
              <p className='text-sm text-red-600'>{message}</p>
            </div>

            <div className='space-y-3'>
              <Button
                onClick={handleGoBack}
                variant='outline'
                className='w-full border-red-300 text-red-700 hover:bg-red-50'
              >
                <ArrowLeft className='mr-2 h-4 w-4' />
                Back to Sign In
              </Button>

              <Button
                onClick={handleResendVerification}
                disabled={isResending}
                className='w-full bg-red-600 hover:bg-red-700 text-white'
              >
                <Mail className='mr-2 h-4 w-4' />
                {isResending ? 'Sending...' : 'Resend Verification Email'}
              </Button>
            </div>

            <div className='bg-white p-4 rounded-lg border border-red-200'>
              <h3 className='text-sm font-medium text-gray-900 mb-2'>
                Common Issues:
              </h3>
              <ul className='text-sm text-gray-600 space-y-1'>
                <li>• The verification link may have expired</li>
                <li>• The link may have been used already</li>
                <li>• Check if you&apos;re using the correct email</li>
                <li>• Try requesting a new verification email</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function AuthErrorPage() {
  return (
    <Suspense
      fallback={
        <div className='min-h-screen flex items-center justify-center bg-gray-50'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-red-600'></div>
        </div>
      }
    >
      <AuthErrorContent />
    </Suspense>
  );
}
