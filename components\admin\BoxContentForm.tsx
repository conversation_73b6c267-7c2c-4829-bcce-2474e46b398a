'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { Calendar, Save } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import {
  createBoxContentAction,
  updateBoxContentAction,
} from '@/lib/actions/admin/box-contents';

interface BoxContentFormProps {
  initialData?: {
    id?: string;
    week_start_date: string;
    contents: string;
  };
}

export function BoxContentForm({ initialData }: BoxContentFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    week_start_date: initialData?.week_start_date || '',
    contents: initialData?.contents || '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (initialData?.id) {
        // Update existing box content
        const result = await updateBoxContentAction(initialData.id, formData);
        if (!result.success || result.error) {
          throw new Error(result.error || 'Failed to update box content');
        }
      } else {
        // Create new box content
        const result = await createBoxContentAction(formData);
        if (!result.success || result.error) {
          throw new Error(result.error || 'Failed to create box content');
        }
      }

      // Redirect back to box contents list
      router.push('/admin/box-contents');
    } catch (error) {
      console.error('Error saving box content:', error);
      // TODO: Add proper error handling/toast notification
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Get next Monday as default date
  const getNextMonday = () => {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const daysUntilMonday = dayOfWeek === 0 ? 1 : 8 - dayOfWeek; // If Sunday, next Monday is 1 day away
    const nextMonday = new Date(today);
    nextMonday.setDate(today.getDate() + daysUntilMonday);
    return format(nextMonday, 'yyyy-MM-dd');
  };

  // Set default date if not provided
  if (!formData.week_start_date && !initialData) {
    setFormData((prev) => ({
      ...prev,
      week_start_date: getNextMonday(),
    }));
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center space-x-2'>
          <Calendar className='h-5 w-5' />
          <span>{initialData ? 'Edit Box Content' : 'Create Box Content'}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className='space-y-6'>
          <div className='space-y-2'>
            <Label htmlFor='week_start_date'>Week Start Date</Label>
            <Input
              id='week_start_date'
              type='date'
              value={formData.week_start_date}
              onChange={(e) =>
                handleInputChange('week_start_date', e.target.value)
              }
              required
            />
            <p className='text-sm text-gray-500'>
              Select the Monday that starts the week for this box content
            </p>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='contents'>Box Contents</Label>
            <Textarea
              id='contents'
              placeholder='Enter the produce items for this week (e.g., Fresh spinach, organic carrots, bell peppers...)'
              value={formData.contents}
              onChange={(e) => handleInputChange('contents', e.target.value)}
              rows={6}
              required
            />
            <p className='text-sm text-gray-500'>
              List all the produce items that will be included in the boxes for
              this week
            </p>
          </div>

          <div className='flex justify-end space-x-4'>
            <Button
              type='button'
              variant='outline'
              onClick={() => router.back()}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={loading}>
              {loading ? (
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
              ) : (
                <Save className='mr-2 h-4 w-4' />
              )}
              {initialData ? 'Update' : 'Create'} Box Content
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
