// Subscription model constants and types
export const BOX_SIZES = {
  test: {
    id: 'test',
    name: 'Test Box',
    price: 0.5,
    priceCents: 50,
    description: 'Test subscription for $0.5',
    servings: 'Testing only',
    subtitle: 'For testing purposes only',
    isPopular: false,
  },
  small: {
    id: 'small',
    name: 'Small Box',
    price: 25.99,
    priceCents: 2599,
    description: '8-10 seasonal items',
    servings: '1-2 people',
    subtitle: 'Perfect for individuals or couples',
    isPopular: false,
  },
  medium: {
    id: 'medium',
    name: 'Medium Box',
    price: 35.99,
    priceCents: 3599,
    description: '12-15 seasonal items',
    servings: '3-4 people',
    subtitle: 'Great for small families',
    isPopular: true,
  },
  large: {
    id: 'large',
    name: 'Large Box',
    price: 45.99,
    priceCents: 4599,
    description: '18-22 seasonal items',
    servings: '5-6 people',
    subtitle: 'Ideal for large families',
    isPopular: false,
  },
} as const;

export const FREQUENCIES = {
  weekly: {
    id: 'weekly',
    name: 'Weekly Delivery',
    description: 'Fresh produce every week',
    discount: 0,
  },
  biweekly: {
    id: 'biweekly',
    name: 'Bi-weekly Delivery',
    description: 'Fresh produce every two weeks',
    discount: 0,
  },
  monthly: {
    id: 'monthly',
    name: 'Monthly Delivery',
    description: 'Fresh produce once a month',
    discount: 10,
  },
} as const;

export const PAYMENT_PLANS = {
  '4_deliveries': {
    id: '4_deliveries',
    name: '4 Deliveries',
    deliveries: 4,
    discount: 0,
    description: 'Standard pricing',
  },
  '12_deliveries': {
    id: '12_deliveries',
    name: '12 Deliveries',
    deliveries: 12,
    discount: 7,
    description: '7% discount',
    popular: true,
  },
  '24_deliveries': {
    id: '24_deliveries',
    name: '24 Deliveries',
    deliveries: 24,
    discount: 10,
    description: '10% discount',
    bestValue: true,
  },
} as const;

export const PICKUP_LOCATIONS = {
  elite_bodies: {
    id: 'elite_bodies',
    name: 'Elite Bodies',
    address: '10111 Colesville Road, Silver Spring, MD 20910',
    description: 'Silver Spring location',
  },
  shabach_ministries: {
    id: 'shabach_ministries',
    name: 'Shabach! Ministries',
    address: '3600 Brightseat Rd, Glenarden, MD 20706',
    description: 'Glenarden location',
  },
} as const;

export const DELIVERY_TYPES = {
  pickup: {
    id: 'pickup',
    name: 'Pickup Location',
    description: 'Pick up at one of our convenient locations',
  },
} as const;

// Discount calculation based on frequency and payment plan
export const DISCOUNT_TIERS = {
  weekly: {
    '4_deliveries': { discount: 0 },
    '12_deliveries': { discount: 5 },
    '24_deliveries': { discount: 10 },
  },
  biweekly: {
    '4_deliveries': { discount: 0 },
    '12_deliveries': { discount: 7 },
    '24_deliveries': { discount: 10 },
  },
  monthly: {
    '4_deliveries': { discount: 10 },
    '12_deliveries': { discount: 15 },
    '24_deliveries': { discount: 20 },
  },
} as const;

// Type definitions
export type BoxSizeId = keyof typeof BOX_SIZES;
export type FrequencyId = keyof typeof FREQUENCIES;
export type PaymentPlanId = keyof typeof PAYMENT_PLANS;
export type DeliveryTypeId = keyof typeof DELIVERY_TYPES;
export type PickupLocationId = keyof typeof PICKUP_LOCATIONS;

export interface SubscriptionData {
  boxSize: BoxSizeId;
  frequency: FrequencyId;
  paymentPlan: PaymentPlanId;
  deliveryType: DeliveryTypeId;
  pickupLocation: PickupLocationId;
  specialInstructions?: string;
}

export interface PricingCalculation {
  basePrice: number;
  basePriceCents: number;
  frequencyDiscount: number;
  paymentPlanDiscount: number;
  totalDiscount: number;
  discountAmount: number;
  finalPrice: number;
  finalPriceCents: number;
  totalPrice: number;
  totalPriceCents: number;
  savings: number;
  pricePerBox: number;
}

// Utility functions
export const calculatePrice = (
  boxSize: BoxSizeId,
  frequency: FrequencyId,
  paymentPlan: PaymentPlanId
): PricingCalculation => {
  const basePrice = BOX_SIZES[boxSize].price;
  const basePriceCents = BOX_SIZES[boxSize].priceCents;
  const deliveries = PAYMENT_PLANS[paymentPlan].deliveries;

  // Get frequency discount
  const frequencyDiscount = FREQUENCIES[frequency].discount;

  // Get payment plan discount
  const paymentPlanDiscount = DISCOUNT_TIERS[frequency][paymentPlan].discount;

  // Calculate total discount
  const totalDiscount = Math.max(frequencyDiscount, paymentPlanDiscount);

  // Calculate final price per box
  // Calculate everything in cents to avoid floating-point issues
  const discountAmountCents = Math.round(
    (basePriceCents * totalDiscount) / 100
  );
  const finalPriceCents = basePriceCents - discountAmountCents;
  const finalPrice = finalPriceCents / 100;

  // Calculate total price for all pickups
  const totalPrice = finalPrice * deliveries;
  const totalPriceCents = finalPriceCents * deliveries;

  // Calculate savings
  const savings = basePrice * deliveries - totalPrice;

  // Calculate discount amount in dollars
  const discountAmount = discountAmountCents / 100;

  return {
    basePrice,
    basePriceCents,
    frequencyDiscount,
    paymentPlanDiscount,
    totalDiscount,
    discountAmount,
    finalPrice,
    finalPriceCents,
    totalPrice,
    totalPriceCents,
    savings,
    pricePerBox: finalPrice,
  };
};

export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(price);
};

export const getNextPickupDate = (
  frequency: FrequencyId,
  startDate?: Date
): Date => {
  const start = startDate || new Date();
  const nextPickup = new Date(start);

  switch (frequency) {
    case 'weekly':
      nextPickup.setDate(nextPickup.getDate() + 7);
      break;
    case 'biweekly':
      nextPickup.setDate(nextPickup.getDate() + 14);
      break;
    case 'monthly':
      nextPickup.setMonth(nextPickup.getMonth() + 1);
      break;
  }

  return nextPickup;
};

// Keep the old function name for backward compatibility
export const getNextDeliveryDate = getNextPickupDate;
