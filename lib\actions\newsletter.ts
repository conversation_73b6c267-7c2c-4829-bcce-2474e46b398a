'use server';

import { createClient } from '@/lib/supabase/server';
import { EmailService } from '@/lib/services/email';
import { z } from 'zod';

// Validation schema for newsletter subscription
const newsletterSubscriptionSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

export type NewsletterSubscriptionData = z.infer<
  typeof newsletterSubscriptionSchema
>;

export async function subscribeToNewsletterAction(email: string) {
  try {
    // Validate the email
    const validatedData = newsletterSubscriptionSchema.parse({ email });

    const supabase = await createClient();

    // Check if email already exists
    const { data: existingSubscription, error: checkError } = await supabase
      .from('newsletter_subscriptions')
      .select('id, is_active')
      .eq('email', validatedData.email)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      // PGRST116 is "not found" error, which is expected for new emails
      console.error('Error checking existing subscription:', checkError);
      return {
        success: false,
        error: 'Failed to process subscription. Please try again.',
      };
    }

    if (existingSubscription) {
      if (existingSubscription.is_active) {
        return {
          success: false,
          error: 'This email is already subscribed to our newsletter.',
        };
      } else {
        // Reactivate existing subscription
        const { error: updateError } = await supabase
          .from('newsletter_subscriptions')
          .update({ is_active: true })
          .eq('id', existingSubscription.id);

        if (updateError) {
          console.error('Error reactivating subscription:', updateError);
          return {
            success: false,
            error: 'Failed to reactivate subscription. Please try again.',
          };
        }

        // Send welcome email for reactivated subscription
        try {
          await EmailService.sendNewsletterWelcome(validatedData.email);
          await EmailService.addToAudience(validatedData.email);

          // Send admin notification for reactivation
          const adminEmail = process.env.ADMIN_EMAIL;
          if (adminEmail) {
            await EmailService.sendNewsletterSignupNotification(adminEmail, {
              id: existingSubscription.id,
              email: validatedData.email,
              signupDate: new Date(),
              source: 'website',
              isReactivation: true,
            });
            console.log('Newsletter reactivation notification sent to admin');
          }
        } catch (emailError) {
          console.error('Error sending reactivation email:', emailError);
        }

        return {
          success: true,
          message:
            'Welcome back! Your newsletter subscription has been reactivated.',
        };
      }
    }

    // Create new subscription
    const { data: newSubscription, error: insertError } = await supabase
      .from('newsletter_subscriptions')
      .insert({
        email: validatedData.email,
        is_active: true,
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error creating newsletter subscription:', insertError);
      return {
        success: false,
        error: 'Failed to subscribe. Please try again.',
      };
    }

    // Send welcome email and add to Resend audience
    try {
      // Send welcome email
      await EmailService.sendNewsletterWelcome(validatedData.email);

      // Add to Resend audience (for future campaigns)
      await EmailService.addToAudience(validatedData.email);

      // Send admin notification
      const adminEmail = process.env.ADMIN_EMAIL;
      if (adminEmail && newSubscription) {
        await EmailService.sendNewsletterSignupNotification(adminEmail, {
          id: newSubscription.id,
          email: newSubscription.email,
          signupDate: new Date(newSubscription.created_at ?? Date.now()),
          source: 'website',
          isReactivation: false,
        });
        console.log('Newsletter signup notification sent to admin');
      }
    } catch (emailError) {
      // Don't fail the subscription if email sending fails
      console.error(
        'Error sending welcome email or adding to audience:',
        emailError
      );
    }

    return {
      success: true,
      message:
        'Thank you for subscribing! Check your email for a welcome message.',
    };
  } catch (error) {
    console.error('Newsletter subscription error:', error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error:
          error.errors[0]?.message || 'Please enter a valid email address.',
      };
    }

    return {
      success: false,
      error: 'An unexpected error occurred. Please try again.',
    };
  }
}
