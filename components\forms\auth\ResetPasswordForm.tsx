'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

import { PasswordInput } from '@/components/ui/password-input';
import { authWithToast, handleAuthError } from '@/lib/utils/auth';
import {
  resetPasswordSchema,
  type ResetPasswordFormData,
  validatePasswordStrength,
} from '@/lib/validations/auth-schemas';

interface ResetPasswordFormProps {
  onSuccess?: () => void;
  redirectTo?: string;
  className?: string;
}

export default function ResetPasswordForm({
  onSuccess,
  redirectTo = '/login?message=Password updated successfully',
  className,
}: ResetPasswordFormProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const form = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });

  const watchPassword = form.watch('password');
  const passwordStrength = watchPassword
    ? validatePasswordStrength(watchPassword)
    : null;

  useEffect(() => {
    // Check if we have the required tokens in the URL
    const accessToken = searchParams.get('access_token');
    const refreshToken = searchParams.get('refresh_token');

    if (!accessToken || !refreshToken) {
      setError('Invalid reset link. Please request a new password reset.');
    }
  }, [searchParams]);

  const onSubmit = async (data: ResetPasswordFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      // The toast will handle the promise and show appropriate messages
      await authWithToast.resetPassword(data.password);

      // If we reach here, password was updated successfully
      setIsSuccess(true);

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
        return;
      }

      // Redirect after 3 seconds
      setTimeout(() => {
        router.push(redirectTo);
      }, 3000);
    } catch (err) {
      console.error('Reset password error:', err);
      handleAuthError(err);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <div className={className}>
        <div className='text-center space-y-4'>
          <div className='mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100'>
            <svg
              className='h-6 w-6 text-green-600'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M5 13l4 4L19 7'
              />
            </svg>
          </div>
          <div>
            <h3 className='text-lg font-medium text-gray-900'>
              Password updated!
            </h3>
            <p className='mt-2 text-sm text-gray-600'>
              Your password has been successfully updated. You&apos;ll be
              redirected to the login page in a few seconds.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
          {error && (
            <div className='bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm'>
              {error}
            </div>
          )}

          <div className='text-center mb-6'>
            <h2 className='text-2xl font-bold text-gray-900'>
              Reset your password
            </h2>
            <p className='mt-2 text-sm text-gray-600'>
              Enter your new password below.
            </p>
          </div>

          <FormField
            control={form.control}
            name='password'
            render={({ field }) => (
              <FormItem>
                <FormLabel>New Password</FormLabel>
                <FormControl>
                  <PasswordInput
                    placeholder='Enter your new password'
                    autoComplete='new-password'
                    id='reset-password'
                    toggleLabel='Show password'
                    {...field}
                  />
                </FormControl>
                {passwordStrength && (
                  <div className='mt-2 space-y-1'>
                    <div className='flex space-x-1'>
                      {Array.from({ length: 4 }).map((_, i) => (
                        <div
                          key={i}
                          className={`h-1 flex-1 rounded ${
                            i < passwordStrength.score
                              ? passwordStrength.score <= 2
                                ? 'bg-red-500'
                                : passwordStrength.score === 3
                                  ? 'bg-yellow-500'
                                  : 'bg-green-500'
                              : 'bg-gray-200'
                          }`}
                        />
                      ))}
                    </div>
                    <div className='text-xs space-y-1'>
                      {passwordStrength.requirements.map((req, i) => (
                        <div
                          key={i}
                          className={`flex items-center space-x-1 ${
                            req.satisfied ? 'text-green-600' : 'text-gray-500'
                          }`}
                        >
                          <span>{req.satisfied ? '✓' : '○'}</span>
                          <span>{req.message}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='confirmPassword'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Confirm New Password</FormLabel>
                <FormControl>
                  <PasswordInput
                    placeholder='Confirm your new password'
                    autoComplete='new-password'
                    id='reset-confirm-password'
                    toggleLabel='Show password'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            type='submit'
            disabled={isLoading || !!error}
            className='w-full'
          >
            {isLoading ? 'Updating password...' : 'Update password'}
          </Button>
        </form>
      </Form>
    </div>
  );
}
