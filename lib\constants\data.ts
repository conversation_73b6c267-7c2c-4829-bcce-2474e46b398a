import {
  ArrowRight,
  Check,
  Globe,
  Leaf,
  MessageCircle,
  Plus,
  Smile,
  Target,
} from 'lucide-react';
import {
  africanManHarvestingVegetables,
  diverseLocalVendors,
  smileyWoman,
} from './imgs';

const aboutQualities = [
  {
    id: 1,
    title: 'Community',
    icon: Plus,
    message:
      'Building stronger connections between farmers and families through food.',
  },
  {
    id: 2,
    title: 'Gratitude',
    icon: Smile,
    message:
      'Expressing appreciation through every aspect of our service and operations.',
  },
  {
    id: 3,
    title: 'Sustainability',
    icon: Globe,
    message: 'Caring for the land that feeds us through responsible practices.',
  },
];

const benefits = [
  {
    id: 1,
    title: 'Farm Fresh Produce',
    icon: Target,
    message:
      'Our produce goes from farm to your table in 24-48 hours, ensuring maximum freshness and nutritional value.',
  },
  {
    id: 2,
    title: 'Support Local Farmers',
    icon: Check,
    message:
      'We partner with small, local farms within 100 miles, ensuring your money supports your local economy.',
  },
  {
    id: 3,
    title: 'Flexible Subscription',
    icon: Plus,
    message:
      'Choose the box size and delivery frequency that suits your needs, and modify or pause your subscription anytime.',
  },
  {
    id: 4,
    title: 'Environmentally Friendly',
    icon: Leaf,
    message:
      'We use minimal and biodegradable packaging, reducing waste while delivering fresh produce.',
  },
  {
    id: 5,
    title: 'Recipe Inspiration',
    icon: MessageCircle,
    message:
      'Each delivery includes seasonal recipes that make the most of your fresh produce.',
  },
  {
    id: 6,
    title: 'Convenient Pickup',
    icon: ArrowRight,
    message:
      'Pick up at locations near you on a schedule that works for you. We bring fresh food to your community.',
  },
];

const processSteps = [
  {
    id: 1,
    step: '1',
    title: 'Choose Your Box',
    description:
      'Select the box size that fits your household needs. Small, medium, or large options available.',
  },
  {
    id: 2,
    step: '2',
    title: 'Set Your Schedule',
    description:
      'Choose weekly, bi-weekly, or monthly deliveries that fit your lifestyle and consumption.',
  },
  {
    id: 3,
    step: '3',
    title: 'We Harvest & Distribute',
    description:
      'Our farmers pick your produce at peak freshness and we bring it to convenient pickup locations.',
  },
  {
    id: 4,
    step: '4',
    title: 'Enjoy Fresh Produce',
    description:
      'Cook with the freshest seasonal ingredients and enjoy the flavors of local farming.',
  },
];

const subscriptionPlans = [
  {
    id: 1,
    name: 'Small Box',
    description: 'Perfect for individuals or couples',
    price: '$25.99',
    priceNote: 'per delivery',
    features: [
      '8-10 seasonal items',
      'Recipe suggestions included',
      'Feeds 1-2 people for a week',
    ],
    buttonText: 'Subscribe Now',
    isPopular: false,
    buttonVariant: 'outline' as const,
  },
  {
    id: 2,
    name: 'Medium Box',
    description: 'Great for small families',
    price: '$35.99',
    priceNote: 'per delivery',
    features: [
      '12-15 seasonal items',
      'Recipe suggestions included',
      'Feeds 3-4 people for a week',
      'Seasonal herb bundle included',
    ],
    buttonText: 'Subscribe Now',
    isPopular: true,
    buttonVariant: 'default' as const,
  },
  {
    id: 3,
    name: 'Large Box',
    description: 'Perfect for larger families',
    price: '$45.99',
    priceNote: 'per delivery',
    features: [
      '18-22 seasonal items',
      'Recipe suggestions included',
      'Feeds 5+ people for a week',
      'Premium seasonal extras included',
    ],
    buttonText: 'Subscribe Now',
    isPopular: false,
    buttonVariant: 'outline' as const,
  },
];

const pickupFrequencies = [
  {
    id: 1,
    name: 'Weekly',
    description: 'Fresh produce every week',
    discount: null,
  },
  {
    id: 2,
    name: 'Bi-weekly',
    description: 'Fresh produce every other week (5% discount)',
    discount: '5%',
  },
  {
    id: 3,
    name: 'Monthly',
    description: 'Fresh produce once a month (10% discount)',
    discount: '10%',
  },
];

const freshSelectionImages = [
  {
    id: 1,
    src: africanManHarvestingVegetables,
    alt: 'African man harvesting fresh vegetables from the farm',
  },
  {
    id: 2,
    src: diverseLocalVendors,
    alt: 'Diverse local vendors preparing healthy market counter',
  },
  {
    id: 3,
    src: smileyWoman,
    alt: 'Smiling woman in agricultural field',
  },
];

const faqData = [
  {
    id: 1,
    question: 'How does delivery work?',
    answer:
      "We deliver to specific pickup locations throughout Maryland on the same day each week (or according to your chosen frequency). You'll receive a text message when your box is ready for pickup. Our convenient pickup locations include community centers, churches, and partner businesses. Our packaging keeps produce fresh for up to 8 hours after delivery to pickup points.",
  },
  {
    id: 2,
    question: "Can I customize what's in my box?",
    answer:
      "Our boxes are curated based on what's fresh and in season. While we don't offer full customization, subscribers can set up to 3 \"never send\" items for produce they dislike or are allergic to. We'll always substitute with something of equal or greater value that's in season.",
  },
  {
    id: 3,
    question: 'How do I pause or cancel my subscription?',
    answer:
      'You can easily pause, skip deliveries, or cancel your subscription by logging into your account. We ask for 48 hours notice before your scheduled delivery day for any changes. There are no cancellation fees or minimum commitment periods.',
  },
  {
    id: 4,
    question: 'Where do you source your produce?',
    answer:
      'All our produce comes from small, local farms within 100 miles of our distribution center in Maryland. We partner with farmers who use sustainable growing practices. Each box includes information about the farms that grew your food.',
  },
  {
    id: 5,
    question: 'What are the pickup locations?',
    answer:
      'We have pickup points throughout Maryland, focusing on accessible community locations. Current pickup points include Baltimore, Annapolis, Silver Spring, Bethesda, Frederick, and several other locations. Check our zip code on our website to find the nearest pickup point.',
  },
  {
    id: 6,
    question: 'What about packaging?',
    answer:
      "We're committed to minimal, eco-friendly packaging. Our boxes are made from recycled cardboard and are 100% recyclable or compostable. We use biodegradable produce bags when necessary, and encourage customers to return boxes and packaging materials at pickup points for reuse.",
  },
];

export {
  aboutQualities,
  benefits,
  processSteps,
  subscriptionPlans,
  pickupFrequencies,
  freshSelectionImages,
  faqData,
};
