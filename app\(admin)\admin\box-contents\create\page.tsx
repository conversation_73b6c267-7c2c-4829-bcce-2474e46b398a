import { redirect } from 'next/navigation';
import Link from 'next/link';
import { authServer } from '@/lib/utils/auth-server';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { BoxContentForm } from '@/components/admin';

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

export default async function CreateBoxContentPage() {
  const { data: user } = await authServer.getCurrentUser();

  if (!user) {
    redirect('/login');
  }

  if (user.role !== 'admin') {
    redirect('/dashboard');
  }

  return (
    <div className='space-y-4 md:space-y-6'>
      <div className='flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4'>
        <Link href='/admin/box-contents'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to Box Contents
          </Button>
        </Link>
        <div>
          <h1 className='text-2xl md:text-3xl font-bold text-gray-900'>
            Create Box Content
          </h1>
          <p className='text-gray-600 mt-2 text-sm md:text-base'>
            Add new weekly produce box contents
          </p>
        </div>
      </div>

      <div className='max-w-2xl'>
        <BoxContentForm />
      </div>
    </div>
  );
}
