import {
  Text,
  Section,
  Row,
  Column,
} from '@react-email/components';
import { EmailLayout } from '../../components/EmailLayout';
import { EmailButton } from '../../components/EmailButton';
import { formatDate } from '@/lib/utils/format';

interface SubscriptionPauseProps {
  subscriberName: string;
  subscriptionData: {
    boxSize: string;
    frequency: string;
    pauseStartDate: Date;
    pauseEndDate: Date;
    resumeDate: Date;
  };
}

export function SubscriptionPause({
  subscriberName,
  subscriptionData,
}: SubscriptionPauseProps) {
  const { boxSize, frequency, pauseStartDate, pauseEndDate, resumeDate } = subscriptionData;

  return (
    <EmailLayout preview="Your subscription has been paused">
      <Text
        style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#1f2937',
          margin: '0 0 20px 0',
        }}
      >
        Subscription Paused
      </Text>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 0',
        }}
      >
        Hi {subscriberName}, your AsedaFoods CSA subscription has been successfully paused. We&apos;ll hold your spot and resume deliveries when you&apos;re ready!
      </Text>

      <Section
        style={{
          backgroundColor: '#fef3c7',
          padding: '20px',
          borderRadius: '8px',
          margin: '20px 0',
          border: '1px solid #fbbf24',
        }}
      >
        <Text
          style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#92400e',
            margin: '0 0 15px 0',
          }}
        >
          Pause Details
        </Text>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#78350f', margin: '0' }}>
              Box Size:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#92400e',
                margin: '0',
              }}
            >
              {boxSize}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#78350f', margin: '0' }}>
              Frequency:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#92400e',
                margin: '0',
              }}
            >
              {frequency}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#78350f', margin: '0' }}>
              Pause Start Date:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#92400e',
                margin: '0',
              }}
            >
              {formatDate(pauseStartDate)}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#78350f', margin: '0' }}>
              Pause End Date:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#92400e',
                margin: '0',
              }}
            >
              {formatDate(pauseEndDate)}
            </Text>
          </Column>
        </Row>

        <Row>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#78350f', margin: '0' }}>
              Resume Date:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#92400e',
                margin: '0',
              }}
            >
              {formatDate(resumeDate)}
            </Text>
          </Column>
        </Row>
      </Section>

      <Section
        style={{
          backgroundColor: '#ecfdf5',
          padding: '15px',
          borderRadius: '8px',
          margin: '20px 0',
          border: '1px solid #86efac',
        }}
      >
        <Text
          style={{
            fontSize: '14px',
            color: '#166534',
            margin: '0',
          }}
        >
          <strong>Good news!</strong> Your subscription preferences, pickup location, and &quot;never send&quot; items will all be preserved during the pause. When your subscription resumes, everything will be exactly as you left it.
        </Text>
      </Section>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '20px 0',
        }}
      >
        During your pause period:
      </Text>

      <ul
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 20px',
          paddingLeft: '0',
        }}
      >
        <li style={{ marginBottom: '8px' }}>
          No deliveries will be scheduled
        </li>
        <li style={{ marginBottom: '8px' }}>
          No payments will be charged
        </li>
        <li style={{ marginBottom: '8px' }}>
          Your account and preferences remain active
        </li>
        <li style={{ marginBottom: '8px' }}>
          You can resume early anytime from your dashboard
        </li>
      </ul>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '20px 0',
        }}
      >
        Need to resume early or make changes to your pause period? You can manage everything from your dashboard.
      </Text>

      <EmailButton href="https://asedafoods.org/dashboard" text="Manage Subscription" />

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '30px 0 0 0',
        }}
      >
        We&apos;ll send you a reminder email a few days before your subscription is scheduled to resume on {formatDate(resumeDate)}.
      </Text>

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '20px 0 0 0',
        }}
      >
        Thank you for being part of the AsedaFoods community. We look forward to serving you fresh, local produce again soon!
      </Text>
    </EmailLayout>
  );
}
