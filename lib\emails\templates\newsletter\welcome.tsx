import { Text } from '@react-email/components';
import { EmailLayout } from '../../components/EmailLayout';
import { EmailButton } from '../../components/EmailButton';

interface NewsletterWelcomeProps {
  email: string;
}

export function NewsletterWelcome({}: NewsletterWelcomeProps) {
  return (
    <EmailLayout preview='Welcome to AsedaFoods Newsletter!'>
      <Text
        style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#1f2937',
          margin: '0 0 20px 0',
        }}
      >
        Welcome to AsedaFoods!
      </Text>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 0',
        }}
      >
        Thank you for subscribing to our newsletter! We&apos;re excited to have
        you join our community of local food supporters.
      </Text>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '20px 0',
        }}
      >
        Ready to start your fresh food journey? Explore our subscription options
        and get farm-fresh produce delivered to convenient pickup locations near
        you.
      </Text>

      <EmailButton
        href='https://asedafoods.org/get-a-box'
        text='Start Your Subscription'
      />

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '20px 0 0 0',
        }}
      >
        Questions? Just reply to this email or visit our{' '}
        <a href='https://asedafoods.org/contact' style={{ color: '#16a34a' }}>
          contact page
        </a>
        . We&apos;re here to help!
      </Text>
    </EmailLayout>
  );
}
