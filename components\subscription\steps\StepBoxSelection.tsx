'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, MapPin } from 'lucide-react';
import {
  BOX_SIZES,
  FREQUENCIES,
  PICKUP_LOCATIONS,
  type BoxSizeId,
  type FrequencyId,
  type PickupLocationId,
} from '@/lib/constants/subscription';

interface StepBoxSelectionProps {
  selectedBoxSize?: BoxSizeId;
  selectedFrequency?: FrequencyId;
  selectedPickupLocation?: PickupLocationId;
  onBoxSizeChange: (boxSize: BoxSizeId) => void;
  onFrequencyChange: (frequency: FrequencyId) => void;
  onPickupLocationChange: (pickupLocation: PickupLocationId) => void;
}

export default function StepBoxSelection({
  selectedBoxSize,
  selectedFrequency,
  selectedPickupLocation,
  onBoxSizeChange,
  onFrequencyChange,
  onPickupLocationChange,
}: StepBoxSelectionProps) {
  return (
    <div className='space-y-8'>
      {/* Box Size Selection */}
      <div>
        <h3 className='text-lg font-semibold mb-4'>Choose Your Box Size</h3>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
          {Object.values(BOX_SIZES).map((box) => (
            <Card
              key={box.id}
              role='button'
              tabIndex={0}
              aria-pressed={selectedBoxSize === box.id}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  onBoxSizeChange(box.id);
                }
              }}
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedBoxSize === box.id
                  ? 'ring-2 ring-green-600 border-green-600'
                  : 'border-gray-200'
              }`}
              onClick={() => onBoxSizeChange(box.id)}
            >
              <CardContent className='p-6'>
                <div className='flex justify-between items-start mb-3'>
                  <div>
                    <h4 className='font-semibold text-lg'>{box.name}</h4>
                    {box.isPopular && (
                      <Badge className='mt-1 bg-green-100 text-green-800'>
                        Most Popular
                      </Badge>
                    )}
                  </div>
                  {selectedBoxSize === box.id && (
                    <div className='w-6 h-6 bg-green-600 rounded-full flex items-center justify-center'>
                      <Check className='w-4 h-4 text-white' />
                    </div>
                  )}
                </div>

                <div className='space-y-2'>
                  <div className='text-sm text-gray-600'>{box.description}</div>
                  <div className='text-sm text-gray-600'>
                    Serves {box.servings}
                  </div>
                  <div className='text-sm text-gray-500'>{box.subtitle}</div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Frequency Selection */}
      <div>
        <h3 className='text-lg font-semibold mb-4'>Delivery Frequency</h3>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
          {Object.values(FREQUENCIES).map((frequency) => (
            <Card
              key={frequency.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedFrequency === frequency.id
                  ? 'ring-2 ring-green-600 border-green-600'
                  : 'border-gray-200'
              }`}
              onClick={() => onFrequencyChange(frequency.id)}
            >
              <CardContent className='p-4'>
                <div className='flex justify-between items-start mb-2'>
                  <h4 className='font-semibold'>{frequency.name}</h4>
                  {selectedFrequency === frequency.id && (
                    <div className='w-5 h-5 bg-green-600 rounded-full flex items-center justify-center'>
                      <Check className='w-3 h-3 text-white' />
                    </div>
                  )}
                </div>
                <div className='text-sm text-gray-600 mb-2'>
                  {frequency.description}
                </div>
                {frequency.discount > 0 && (
                  <Badge className='bg-orange-100 text-orange-800'>
                    {frequency.discount}% off
                  </Badge>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Pickup Location Selection */}
      <div>
        <h3 className='text-lg font-semibold mb-4'>Choose Pickup Location</h3>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          {Object.values(PICKUP_LOCATIONS).map((location) => (
            <Card
              key={location.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedPickupLocation === location.id
                  ? 'ring-2 ring-green-600 border-green-600'
                  : 'border-gray-200'
              }`}
              onClick={() => onPickupLocationChange(location.id)}
            >
              <CardContent className='p-4'>
                <div className='flex items-start space-x-3'>
                  <div className='mt-1'>
                    <MapPin className='w-5 h-5 text-green-600' />
                  </div>
                  <div className='flex-1'>
                    <div className='flex justify-between items-start'>
                      <h4 className='font-semibold'>{location.name}</h4>
                      {selectedPickupLocation === location.id && (
                        <div className='w-5 h-5 bg-green-600 rounded-full flex items-center justify-center'>
                          <Check className='w-3 h-3 text-white' />
                        </div>
                      )}
                    </div>
                    <div className='text-sm text-gray-600 mt-1'>
                      {location.description}
                    </div>
                    <div className='text-xs text-gray-500 mt-2'>
                      {location.address}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
