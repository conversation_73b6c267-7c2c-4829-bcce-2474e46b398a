'use client';

import { GuestSubscriptionWizard, AuthenticatedSubscriptionWizard } from './SubscriptionWizardWrapper';

interface SubscriptionWizardRendererProps {
  userType: 'guest' | 'authenticated';
  subscriber?: any;
  user?: any;
}

export function SubscriptionWizardRenderer({ 
  userType, 
  subscriber, 
  user 
}: SubscriptionWizardRendererProps) {
  if (userType === 'guest') {
    return <GuestSubscriptionWizard />;
  }

  return <AuthenticatedSubscriptionWizard subscriber={subscriber} user={user} />;
}
