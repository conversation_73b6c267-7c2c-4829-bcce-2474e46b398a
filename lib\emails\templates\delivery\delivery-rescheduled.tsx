import {
  Body,
  But<PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface DeliveryRescheduledProps {
  subscriberName: string;
  deliveryData: {
    originalDate: Date;
    newDate: Date;
    pickupLocation: string;
    boxSize: string;
    rescheduleReason?: string;
    deliveryId: string;
  };
}

const baseUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : 'http://localhost:3000';

export const DeliveryRescheduled = ({
  subscriberName = 'Valued Customer',
  deliveryData = {
    originalDate: new Date(),
    newDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
    pickupLocation: 'shabach_ministries',
    boxSize: 'medium',
    rescheduleReason: 'Weather conditions',
    deliveryId: 'delivery-123',
  },
}: DeliveryRescheduledProps) => {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatLocation = (location: string) => {
    return location.replace('_', ' ').toUpperCase();
  };

  return (
    <Html>
      <Head />
      <Preview>Your delivery has been rescheduled to a new date</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Img
              src={`${baseUrl}/logo.png`}
              width='120'
              height='36'
              alt='AsedaFoods'
              style={logo}
            />
          </Section>

          <Heading style={h1}>Delivery Rescheduled 📅</Heading>

          <Text style={text}>Hi {subscriberName},</Text>

          <Text style={text}>
            We need to reschedule your upcoming delivery. We apologize for any
            inconvenience and appreciate your understanding.
          </Text>

          <Section style={rescheduleCard}>
            <Heading style={h2}>Schedule Update</Heading>

            <div style={dateChangeContainer}>
              <div style={originalDateRow}>
                <strong>📅 Original Date:</strong>
                <span style={originalDate}>
                  {formatDate(deliveryData.originalDate)}
                </span>
              </div>

              <div style={arrowContainer}>⬇️</div>

              <div style={newDateRow}>
                <strong>📅 New Date:</strong>
                <span style={newDate}>{formatDate(deliveryData.newDate)}</span>
              </div>
            </div>

            <div style={detailRow}>
              <strong>📍 Location:</strong>{' '}
              {formatLocation(deliveryData.pickupLocation)}
            </div>

            <div style={detailRow}>
              <strong>📦 Box Size:</strong>{' '}
              {deliveryData.boxSize.charAt(0).toUpperCase() +
                deliveryData.boxSize.slice(1)}
            </div>

            {deliveryData.rescheduleReason && (
              <div style={reasonContainer}>
                <strong>💡 Reason for Reschedule:</strong>
                <div style={reasonText}>{deliveryData.rescheduleReason}</div>
              </div>
            )}
          </Section>

          <Section style={importantCard}>
            <Heading style={h2}>Important Notes</Heading>
            <ul style={notesList}>
              <li style={notesItem}>
                📦 Your box contents remain the same - fresh, seasonal produce
              </li>
              <li style={notesItem}>
                🕒 Pickup hours remain the same for your location
              </li>
              <li style={notesItem}>
                💚 No additional charges for this reschedule
              </li>
              <li style={notesItem}>
                📱 Your dashboard has been updated with the new date
              </li>
            </ul>
          </Section>

          <Section style={buttonContainer}>
            <Button
              style={button}
              href={`${baseUrl}/dashboard/deliveries/${deliveryData.deliveryId}`}
            >
              View Updated Delivery
            </Button>
          </Section>

          <Text style={text}>
            If the new date doesn&apos;t work for you or if you have any
            questions, please contact us as soon as possible. We&apos;re here to
            help make this as convenient as possible for you.
          </Text>

          <Section style={contactContainer}>
            <Button style={contactButton} href={`${baseUrl}/contact`}>
              Contact Us
            </Button>
          </Section>

          <Hr style={hr} />

          <Text style={footer}>
            Thank you for your flexibility and continued support of local
            farmers! 🌱
          </Text>

          <Text style={footer}>
            Questions about your rescheduled delivery? Reply to this email or
            visit our{' '}
            <Link href={`${baseUrl}/contact`} style={link}>
              contact page
            </Link>
            .
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

export default DeliveryRescheduled;

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
};

const logoContainer = {
  margin: '32px 0',
  textAlign: 'center' as const,
};

const logo = {
  margin: '0 auto',
};

const h1 = {
  color: '#f59e0b',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0',
  padding: '0',
  textAlign: 'center' as const,
};

const h2 = {
  color: '#333',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
  padding: '0',
};

const text = {
  color: '#333',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '16px',
  lineHeight: '26px',
  margin: '16px 32px',
};

const rescheduleCard = {
  backgroundColor: '#fef3c7',
  border: '1px solid #fbbf24',
  borderRadius: '8px',
  margin: '24px 32px',
  padding: '24px',
};

const importantCard = {
  backgroundColor: '#f0f9ff',
  border: '1px solid #bae6fd',
  borderRadius: '8px',
  margin: '24px 32px',
  padding: '24px',
};

const dateChangeContainer = {
  textAlign: 'center' as const,
  margin: '20px 0',
  padding: '16px',
  backgroundColor: '#ffffff',
  borderRadius: '6px',
  border: '1px solid #e5e7eb',
};

const originalDateRow = {
  margin: '8px 0',
  fontSize: '16px',
  lineHeight: '24px',
};

const originalDate = {
  textDecoration: 'line-through',
  color: '#6b7280',
  marginLeft: '8px',
};

const arrowContainer = {
  margin: '12px 0',
  fontSize: '20px',
};

const newDateRow = {
  margin: '8px 0',
  fontSize: '16px',
  lineHeight: '24px',
};

const newDate = {
  color: '#059669',
  fontWeight: 'bold',
  marginLeft: '8px',
};

const detailRow = {
  margin: '12px 0',
  fontSize: '16px',
  lineHeight: '24px',
};

const reasonContainer = {
  margin: '16px 0 0 0',
  padding: '12px',
  backgroundColor: '#ffffff',
  borderRadius: '4px',
  border: '1px solid #e5e7eb',
};

const reasonText = {
  margin: '8px 0 0 0',
  fontSize: '16px',
  lineHeight: '24px',
  color: '#374151',
  fontStyle: 'italic',
};

const notesList = {
  margin: '0',
  padding: '0 0 0 16px',
};

const notesItem = {
  margin: '8px 0',
  fontSize: '16px',
  lineHeight: '24px',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#22c55e',
  borderRadius: '6px',
  color: '#fff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '16px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '200px',
  padding: '12px 0',
  margin: '0 auto',
};

const contactContainer = {
  textAlign: 'center' as const,
  margin: '16px 0',
};

const contactButton = {
  backgroundColor: '#f59e0b',
  borderRadius: '6px',
  color: '#fff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '14px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '150px',
  padding: '10px 0',
  margin: '0 auto',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '32px 0',
};

const footer = {
  color: '#8898aa',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '14px',
  lineHeight: '24px',
  margin: '16px 32px',
  textAlign: 'center' as const,
};

const link = {
  color: '#22c55e',
  textDecoration: 'underline',
};
