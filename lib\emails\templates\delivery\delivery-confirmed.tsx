import {
  Body,
  But<PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface DeliveryConfirmedProps {
  subscriberName: string;
  deliveryData: {
    deliveryDate: Date;
    pickupLocation: string;
    boxSize: string;
    boxContents?: string[];
    nextDeliveryDate?: Date;
    deliveriesRemaining?: number;
    deliveryId: string;
  };
}

const baseUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : 'http://localhost:3000';

export const DeliveryConfirmed = ({
  subscriberName = 'Valued Customer',
  deliveryData = {
    deliveryDate: new Date(),
    pickupLocation: 'shabach_ministries',
    boxSize: 'medium',
    boxContents: ['Fresh seasonal vegetables', 'Organic fruits', 'Local herbs'],
    nextDeliveryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    deliveriesRemaining: 3,
    deliveryId: 'delivery-123',
  },
}: DeliveryConfirmedProps) => {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatLocation = (location: string) => {
    return location.replace('_', ' ').toUpperCase();
  };

  return (
    <Html>
      <Head />
      <Preview>
        Delivery confirmed! Thank you for picking up your fresh box ✅
      </Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Img
              src={`${baseUrl}/logo.png`}
              width='120'
              height='36'
              alt='AsedaFoods'
              style={logo}
            />
          </Section>

          <Heading style={h1}>Delivery Confirmed! ✅</Heading>

          <Text style={text}>Hi {subscriberName},</Text>

          <Text style={text}>
            Thank you for picking up your fresh produce box! We hope you enjoy
            all the delicious, locally-sourced items.
          </Text>

          <Section style={deliveryCard}>
            <Heading style={h2}>Completed Delivery</Heading>

            <div style={detailRow}>
              <strong>📅 Pickup Date:</strong>{' '}
              {formatDate(deliveryData.deliveryDate)}
            </div>

            <div style={detailRow}>
              <strong>📍 Location:</strong>{' '}
              {formatLocation(deliveryData.pickupLocation)}
            </div>

            <div style={detailRow}>
              <strong>📦 Box Size:</strong>{' '}
              {deliveryData.boxSize.charAt(0).toUpperCase() +
                deliveryData.boxSize.slice(1)}
            </div>
          </Section>

          {deliveryData.boxContents && deliveryData.boxContents.length > 0 && (
            <Section style={contentsCard}>
              <Heading style={h2}>What Was in Your Box</Heading>
              <ul style={contentsList}>
                {deliveryData.boxContents.map((item, index) => (
                  <li key={index} style={contentsItem}>
                    🌱 {item}
                  </li>
                ))}
              </ul>
            </Section>
          )}

          {deliveryData.nextDeliveryDate && (
            <Section style={nextDeliveryCard}>
              <Heading style={h2}>Your Next Delivery</Heading>
              <div style={detailRow}>
                <strong>📅 Next Pickup:</strong>{' '}
                {formatDate(deliveryData.nextDeliveryDate)}
              </div>
              {deliveryData.deliveriesRemaining && (
                <div style={detailRow}>
                  <strong>📦 Deliveries Remaining:</strong>{' '}
                  {deliveryData.deliveriesRemaining}
                </div>
              )}
            </Section>
          )}

          <Section style={buttonContainer}>
            <Button style={button} href={`${baseUrl}/dashboard`}>
              View Your Dashboard
            </Button>
          </Section>

          <Text style={text}>
            We&apos;d love to hear your feedback! How was your experience with
            this delivery? Your input helps us continue to improve our service.
          </Text>

          <Section style={feedbackContainer}>
            <Button
              style={feedbackButton}
              href={`${baseUrl}/feedback?delivery=${deliveryData.deliveryId}`}
            >
              Share Feedback
            </Button>
          </Section>

          <Hr style={hr} />

          <Text style={footer}>
            Thank you for supporting local farmers and choosing AsedaFoods! 🌱
          </Text>

          <Text style={footer}>
            Questions about your next delivery? Reply to this email or visit our{' '}
            <Link href={`${baseUrl}/contact`} style={link}>
              contact page
            </Link>
            .
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

export default DeliveryConfirmed;

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
};

const logoContainer = {
  margin: '32px 0',
  textAlign: 'center' as const,
};

const logo = {
  margin: '0 auto',
};

const h1 = {
  color: '#333',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0',
  padding: '0',
  textAlign: 'center' as const,
};

const h2 = {
  color: '#333',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
  padding: '0',
};

const text = {
  color: '#333',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '16px',
  lineHeight: '26px',
  margin: '16px 32px',
};

const deliveryCard = {
  backgroundColor: '#f0f9ff',
  border: '1px solid #bae6fd',
  borderRadius: '8px',
  margin: '24px 32px',
  padding: '24px',
};

const contentsCard = {
  backgroundColor: '#f0fdf4',
  border: '1px solid #bbf7d0',
  borderRadius: '8px',
  margin: '24px 32px',
  padding: '24px',
};

const nextDeliveryCard = {
  backgroundColor: '#fef3c7',
  border: '1px solid #fbbf24',
  borderRadius: '8px',
  margin: '24px 32px',
  padding: '24px',
};

const detailRow = {
  margin: '12px 0',
  fontSize: '16px',
  lineHeight: '24px',
};

const contentsList = {
  margin: '0',
  padding: '0 0 0 16px',
};

const contentsItem = {
  margin: '8px 0',
  fontSize: '16px',
  lineHeight: '24px',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#22c55e',
  borderRadius: '6px',
  color: '#fff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '16px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '200px',
  padding: '12px 0',
  margin: '0 auto',
};

const feedbackContainer = {
  textAlign: 'center' as const,
  margin: '16px 0',
};

const feedbackButton = {
  backgroundColor: '#3b82f6',
  borderRadius: '6px',
  color: '#fff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '14px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '150px',
  padding: '10px 0',
  margin: '0 auto',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '32px 0',
};

const footer = {
  color: '#8898aa',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '14px',
  lineHeight: '24px',
  margin: '16px 32px',
  textAlign: 'center' as const,
};

const link = {
  color: '#22c55e',
  textDecoration: 'underline',
};
