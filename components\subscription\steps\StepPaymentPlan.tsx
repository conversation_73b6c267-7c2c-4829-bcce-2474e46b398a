'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Star, TrendingUp } from 'lucide-react';
import {
  PAYMENT_PLANS,
  calculatePrice,
  formatPrice,
  type BoxSizeId,
  type FrequencyId,
  type PaymentPlanId,
} from '@/lib/constants/subscription';

interface StepPaymentPlanProps {
  selectedBoxSize?: BoxSizeId;
  selectedFrequency?: FrequencyId;
  selectedPaymentPlan?: PaymentPlanId;
  onPaymentPlanChange: (paymentPlan: PaymentPlanId) => void;
}

export default function StepPaymentPlan({
  selectedBoxSize,
  selectedFrequency,
  selectedPaymentPlan,
  onPaymentPlanChange,
}: StepPaymentPlanProps) {
  if (!selectedBoxSize || !selectedFrequency) {
    return (
      <div className='text-center py-8'>
        <p className='text-gray-500'>
          Please select a box size and frequency first.
        </p>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div>
        <h3 className='text-lg font-semibold mb-2'>Choose Your Payment Plan</h3>
        <p className='text-gray-600 mb-6'>
          Save more with longer commitments. All plans auto-renew unless
          cancelled.
        </p>
      </div>

      <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
        {Object.values(PAYMENT_PLANS).map((plan) => {
          const pricing = calculatePrice(
            selectedBoxSize,
            selectedFrequency,
            plan.id
          );

          return (
            <Card
              key={plan.id}
              className={`cursor-pointer transition-all hover:shadow-lg relative ${
                selectedPaymentPlan === plan.id
                  ? 'ring-2 ring-green-600 border-green-600 shadow-lg'
                  : 'border-gray-200'
              } ${'popular' in plan && plan.popular ? 'scale-105' : ''}`}
              onClick={() => onPaymentPlanChange(plan.id)}
            >
              {'popular' in plan && plan.popular && (
                <div className='absolute -top-3 left-1/4 transform -translate-x-1/2'>
                  <Badge className='bg-blue-600 text-white px-3 py-1'>
                    <Star className='w-3 h-3 mr-1' />
                    Most Popular
                  </Badge>
                </div>
              )}

              {'bestValue' in plan && plan.bestValue && (
                <div className='absolute -top-3 right-1/4 transform translate-x-1/2'>
                  <Badge className='bg-green-600 text-white px-3 py-1'>
                    <TrendingUp className='w-3 h-3 mr-1' />
                    Best Value
                  </Badge>
                </div>
              )}

              <CardContent className='p-6'>
                <div className='flex justify-between items-start mb-4'>
                  <div>
                    <h4 className='font-semibold text-lg'>{plan.name}</h4>
                    <p className='text-sm text-gray-600'>{plan.description}</p>
                  </div>
                  {selectedPaymentPlan === plan.id && (
                    <div className='w-6 h-6 bg-green-600 rounded-full flex items-center justify-center'>
                      <Check className='w-4 h-4 text-white' />
                    </div>
                  )}
                </div>

                <div className='space-y-3'>
                  {/* Price per box */}
                  <div>
                    <div className='flex items-baseline space-x-2'>
                      <span className='text-2xl font-bold text-green-600'>
                        {formatPrice(pricing.pricePerBox)}
                      </span>
                      <span className='text-sm text-gray-500'>per box</span>
                    </div>
                    {pricing.savings > 0 && (
                      <div className='text-sm text-gray-500'>
                        <span className='line-through'>
                          {formatPrice(pricing.basePrice)}
                        </span>
                        <span className='text-green-600 ml-2'>
                          Save {formatPrice(pricing.discountAmount)}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Total price */}
                  <div className='border-t pt-3'>
                    <div className='flex justify-between items-center'>
                      <span className='text-sm text-gray-600'>
                        Total for {plan.deliveries} deliveries:
                      </span>
                      <span className='font-semibold text-lg'>
                        {formatPrice(pricing.totalPrice)}
                      </span>
                    </div>
                    {pricing.savings > 0 && (
                      <div className='flex justify-between items-center text-sm'>
                        <span className='text-gray-500'>You save:</span>
                        <span className='text-green-600 font-medium'>
                          {formatPrice(pricing.savings)}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Discount badge */}
                  {pricing.totalDiscount > 0 && (
                    <div className='pt-2'>
                      <Badge className='bg-orange-100 text-orange-800'>
                        {pricing.totalDiscount}% discount
                      </Badge>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Selected plan summary */}
      {selectedPaymentPlan && (
        <div className='bg-green-50 border border-green-200 rounded-lg p-4'>
          <h4 className='font-semibold text-green-800 mb-2'>
            Payment Plan Summary
          </h4>
          <div className='text-sm text-green-700 space-y-1'>
            {(() => {
              const pricing = calculatePrice(
                selectedBoxSize,
                selectedFrequency,
                selectedPaymentPlan
              );
              const plan = PAYMENT_PLANS[selectedPaymentPlan];

              return (
                <>
                  <div>Plan: {plan.name}</div>
                  <div>Price per box: {formatPrice(pricing.pricePerBox)}</div>
                  <div>Total deliveries: {plan.deliveries}</div>
                  <div>Total cost: {formatPrice(pricing.totalPrice)}</div>
                  {pricing.savings > 0 && (
                    <div className='font-medium'>
                      Total savings: {formatPrice(pricing.savings)}
                    </div>
                  )}
                </>
              );
            })()}
          </div>
        </div>
      )}
    </div>
  );
}
