import Link from 'next/link';
import { NavbarWithAuth } from './NavbarWithAuth';
import { MobileMenuProvider } from './MobileMenuContext';
import { MobileMenuWrapper } from './MobileMenuWrapper';
import { Suspense } from 'react';
import { authServer } from '@/lib/utils/auth-server';
import { PPRErrorBoundary } from '@/components/ui/ppr-error-boundary';

const navLink = [
  { id: 1, title: 'About Us', url: '#aboutus' },
  { id: 3, title: 'How It works', url: '#howitworks' },
  { id: 4, title: 'Plans', url: '#subscriptionplans' },
  { id: 5, title: 'Seasonal Produce', url: '#freshselection' },
  { id: 6, title: 'FAQs', url: '#faqs' },
];

// Server component to fetch user data for mobile menu
async function MobileMenuWithAuth() {
  const { data: user } = await authServer.getCurrentUser();

  // Pass user data as serializable props to client component
  return (
    <MobileMenuWrapper
      user={
        user
          ? {
              id: user.id,
              email: user.email,
              name: user.name,
              role: user.role,
            }
          : null
      }
    />
  );
}

export function Navbar() {
  return (
    <MobileMenuProvider>
      <main className='sticky top-0 z-50 w-full border-neutral-100 border-b bg-white shadow-xs'>
        <nav className='mx-auto max-w-7xl w-full flex items-center justify-between p-4'>
          {/* Static logo and navigation links */}
          <div className='flex items-center space-x-4'>
            <div>
              <Link href={'/'} className='text-xl font-bold'>
                <span className='text-green-600'>Aseda</span>
                <span className='text-green-900'>Foods</span>
              </Link>
            </div>
            <div className='md:flex items-center space-x-6 hidden'>
              {navLink.map((link) => (
                <Link
                  key={link.id}
                  href={link.url}
                  className='font-medium text-neutral-500 hover:text-green-600 hover:underline'
                >
                  {link.title}
                </Link>
              ))}
            </div>
          </div>

          {/* Dynamic authentication section with Suspense */}
          <div className='flex items-center space-x-4'>
            <NavbarWithAuth />
          </div>
        </nav>

        {/* Mobile menu content with Suspense */}
        <PPRErrorBoundary>
          <Suspense fallback={null}>
            <MobileMenuWithAuth />
          </Suspense>
        </PPRErrorBoundary>
      </main>
    </MobileMenuProvider>
  );
}
