/**
 * Stripe Configuration Utility
 * 
 * This utility provides centralized Stripe configuration management that automatically
 * switches between test and live keys based on the environment.
 * 
 * Features:
 * - Automatic environment detection
 * - Type-safe configuration
 * - Error handling for missing keys
 * - Debug information
 * - Support for both client and server environments
 */

export type StripeEnvironment = 'development' | 'production' | 'test';
export type StripeKeyType = 'test' | 'live';

/**
 * Environment detection utilities
 */
export const environmentUtils = {
  /**
   * Determines if we're in development environment
   */
  isDevelopment(): boolean {
    return (
      process.env.NODE_ENV === 'development' || 
      process.env.NODE_ENV === 'test' ||
      process.env.NEXT_PUBLIC_ENVIRONMENT === 'development'
    );
  },

  /**
   * Determines if we're in production environment
   */
  isProduction(): boolean {
    return process.env.NODE_ENV === 'production' && 
           process.env.NEXT_PUBLIC_ENVIRONMENT !== 'development';
  },

  /**
   * Gets the current environment
   */
  getCurrentEnvironment(): StripeEnvironment {
    if (this.isDevelopment()) return 'development';
    if (process.env.NODE_ENV === 'test') return 'test';
    return 'production';
  },

  /**
   * Gets the key type based on environment
   */
  getKeyType(): StripeKeyType {
    return this.isDevelopment() ? 'test' : 'live';
  }
};

/**
 * Stripe key management utilities
 */
export const stripeKeyUtils = {
  /**
   * Gets the appropriate Stripe secret key for server-side usage
   */
  getSecretKey(): string {
    const isDevMode = environmentUtils.isDevelopment();
    
    if (isDevMode) {
      const testKey = process.env.STRIPE_SECRET_TEST_KEY;
      if (!testKey) {
        throw new Error(
          'STRIPE_SECRET_TEST_KEY is not set in environment variables. ' +
          'Please add your Stripe test secret key to your .env file.'
        );
      }
      return testKey;
    } else {
      const liveKey = process.env.STRIPE_SECRET_LIVE_KEY;
      if (!liveKey) {
        throw new Error(
          'STRIPE_SECRET_LIVE_KEY is not set in environment variables. ' +
          'Please add your Stripe live secret key to your .env file.'
        );
      }
      return liveKey;
    }
  },

  /**
   * Gets the appropriate Stripe public key for client-side usage
   */
  getPublicKey(): string {
    const isDevMode = environmentUtils.isDevelopment();
    
    if (isDevMode) {
      const testKey = process.env.NEXT_PUBLIC_STRIPE_PUBLIC_TEST_KEY;
      if (!testKey) {
        throw new Error(
          'NEXT_PUBLIC_STRIPE_PUBLIC_TEST_KEY is not set in environment variables. ' +
          'Please add your Stripe test public key to your .env file.'
        );
      }
      return testKey;
    } else {
      const liveKey = process.env.NEXT_PUBLIC_STRIPE_PUBLIC_LIVE_KEY;
      if (!liveKey) {
        throw new Error(
          'NEXT_PUBLIC_STRIPE_PUBLIC_LIVE_KEY is not set in environment variables. ' +
          'Please add your Stripe live public key to your .env file.'
        );
      }
      return liveKey;
    }
  },

  /**
   * Gets the appropriate webhook secret
   */
  getWebhookSecret(): string | undefined {
    const isDevMode = environmentUtils.isDevelopment();
    
    if (isDevMode) {
      return process.env.STRIPE_WEBHOOK_SECRET_TEST;
    } else {
      return process.env.STRIPE_WEBHOOK_SECRET_LIVE || process.env.STRIPE_WEBHOOK_SECRET;
    }
  }
};

/**
 * Complete Stripe configuration object
 */
export const stripeConfigUtils = {
  /**
   * Gets complete client-side configuration
   */
  getClientConfig() {
    return {
      publicKey: stripeKeyUtils.getPublicKey(),
      currency: 'usd',
      country: 'US',
      environment: environmentUtils.getCurrentEnvironment(),
      keyType: environmentUtils.getKeyType(),
      isDevelopment: environmentUtils.isDevelopment(),
    } as const;
  },

  /**
   * Gets complete server-side configuration
   */
  getServerConfig() {
    return {
      secretKey: stripeKeyUtils.getSecretKey(),
      webhookSecret: stripeKeyUtils.getWebhookSecret(),
      environment: environmentUtils.getCurrentEnvironment(),
      keyType: environmentUtils.getKeyType(),
      isDevelopment: environmentUtils.isDevelopment(),
    } as const;
  },

  /**
   * Gets debug information about current configuration
   */
  getDebugInfo() {
    const isDevMode = environmentUtils.isDevelopment();
    
    return {
      environment: environmentUtils.getCurrentEnvironment(),
      keyType: environmentUtils.getKeyType(),
      isDevelopment: isDevMode,
      nodeEnv: process.env.NODE_ENV,
      publicEnvironment: process.env.NEXT_PUBLIC_ENVIRONMENT,
      hasTestSecretKey: !!process.env.STRIPE_SECRET_TEST_KEY,
      hasLiveSecretKey: !!process.env.STRIPE_SECRET_LIVE_KEY,
      hasTestPublicKey: !!process.env.NEXT_PUBLIC_STRIPE_PUBLIC_TEST_KEY,
      hasLivePublicKey: !!process.env.NEXT_PUBLIC_STRIPE_PUBLIC_LIVE_KEY,
      hasTestWebhookSecret: !!process.env.STRIPE_WEBHOOK_SECRET_TEST,
      hasLiveWebhookSecret: !!process.env.STRIPE_WEBHOOK_SECRET_LIVE,
      hasGenericWebhookSecret: !!process.env.STRIPE_WEBHOOK_SECRET,
    };
  }
};

/**
 * Validation utilities
 */
export const stripeValidationUtils = {
  /**
   * Validates that all required environment variables are set
   */
  validateEnvironmentVariables(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const isDevMode = environmentUtils.isDevelopment();

    if (isDevMode) {
      // Validate test keys
      if (!process.env.STRIPE_SECRET_TEST_KEY) {
        errors.push('STRIPE_SECRET_TEST_KEY is missing');
      }
      if (!process.env.NEXT_PUBLIC_STRIPE_PUBLIC_TEST_KEY) {
        errors.push('NEXT_PUBLIC_STRIPE_PUBLIC_TEST_KEY is missing');
      }
    } else {
      // Validate live keys
      if (!process.env.STRIPE_SECRET_LIVE_KEY) {
        errors.push('STRIPE_SECRET_LIVE_KEY is missing');
      }
      if (!process.env.NEXT_PUBLIC_STRIPE_PUBLIC_LIVE_KEY) {
        errors.push('NEXT_PUBLIC_STRIPE_PUBLIC_LIVE_KEY is missing');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  },

  /**
   * Validates a Stripe key format
   */
  validateKeyFormat(key: string, expectedType: 'secret' | 'public'): boolean {
    if (expectedType === 'secret') {
      return key.startsWith('sk_test_') || key.startsWith('sk_live_');
    } else {
      return key.startsWith('pk_test_') || key.startsWith('pk_live_');
    }
  }
};

// Default export with all utilities
export default {
  environment: environmentUtils,
  keys: stripeKeyUtils,
  config: stripeConfigUtils,
  validation: stripeValidationUtils,
};
