import Stripe from 'stripe';

// Server-side Stripe configuration (only import on server)
if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set in environment variables');
}

// Server-side Stripe instance
export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-05-28.basil',
  typescript: true,
});

// Stripe webhook configuration
export const webhookConfig = {
  secret: process.env.STRIPE_WEBHOOK_SECRET,
} as const;
