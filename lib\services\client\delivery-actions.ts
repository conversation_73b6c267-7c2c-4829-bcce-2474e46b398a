'use client';

/**
 * Client-side delivery action services
 * These services handle user actions and MUST keep loading states for user feedback
 */

export interface DeliveryUpdate {
  status?: 'scheduled' | 'delivered' | 'cancelled';
  delivery_date?: string;
  pickup_location?: string;
  box_contents?: string;
  special_instructions?: string;
}

export const deliveryActions = {
  /**
   * Update delivery status (Admin action)
   * Loading state needed for admin action feedback
   */
  async updateDeliveryStatus(
    deliveryId: string, 
    status: 'scheduled' | 'delivered' | 'cancelled',
    reason?: string
  ) {
    const response = await fetch(`/api/admin/deliveries/${deliveryId}/status`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status, reason }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update delivery status');
    }

    return response.json();
  },

  /**
   * Reschedule delivery (Admin action)
   * Loading state needed for admin action feedback
   */
  async rescheduleDelivery(deliveryId: string, newDate: string, reason?: string) {
    const response = await fetch(`/api/admin/deliveries/${deliveryId}/reschedule`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ delivery_date: newDate, reason }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to reschedule delivery');
    }

    return response.json();
  },

  /**
   * Update delivery details (Admin action)
   * Loading state needed for admin action feedback
   */
  async updateDelivery(deliveryId: string, updates: DeliveryUpdate) {
    const response = await fetch(`/api/admin/deliveries/${deliveryId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update delivery');
    }

    return response.json();
  },

  /**
   * Create new delivery (Admin action)
   * Loading state needed for admin action feedback
   */
  async createDelivery(deliveryData: {
    subscription_id: string;
    delivery_date: string;
    pickup_location: string;
    box_contents?: string;
    special_instructions?: string;
  }) {
    const response = await fetch('/api/admin/deliveries', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(deliveryData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create delivery');
    }

    return response.json();
  },

  /**
   * Delete delivery (Admin action)
   * Loading state needed for admin action feedback
   */
  async deleteDelivery(deliveryId: string) {
    const response = await fetch(`/api/admin/deliveries/${deliveryId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete delivery');
    }

    return response.json();
  },

  /**
   * Mark delivery as picked up (Customer action)
   * Loading state needed for user action feedback
   */
  async markAsPickedUp(deliveryId: string, pickupTime?: string) {
    const response = await fetch(`/api/deliveries/${deliveryId}/pickup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ pickup_time: pickupTime || new Date().toISOString() }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to mark delivery as picked up');
    }

    return response.json();
  },

  /**
   * Report delivery issue (Customer action)
   * Loading state needed for user action feedback
   */
  async reportDeliveryIssue(deliveryId: string, issue: {
    type: 'missing_items' | 'quality_issue' | 'wrong_items' | 'other';
    description: string;
    photos?: string[];
  }) {
    const response = await fetch(`/api/deliveries/${deliveryId}/issue`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(issue),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to report delivery issue');
    }

    return response.json();
  },

  /**
   * Request delivery change (Customer action)
   * Loading state needed for user action feedback
   */
  async requestDeliveryChange(deliveryId: string, change: {
    type: 'reschedule' | 'location_change' | 'special_instructions';
    details: string;
    preferred_date?: string;
    preferred_location?: string;
  }) {
    const response = await fetch(`/api/deliveries/${deliveryId}/change-request`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(change),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to request delivery change');
    }

    return response.json();
  },

  /**
   * Add delivery feedback (Customer action)
   * Loading state needed for user action feedback
   */
  async addDeliveryFeedback(deliveryId: string, feedback: {
    rating: number;
    comment?: string;
    quality_rating?: number;
    delivery_rating?: number;
  }) {
    const response = await fetch(`/api/deliveries/${deliveryId}/feedback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(feedback),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to add delivery feedback');
    }

    return response.json();
  },
};
