import Link from 'next/link';
import { Metadata } from 'next';
import { LoginForm } from '@/components/forms/auth';

export const metadata: Metadata = {
  title: 'Sign In | Aseda Foods',
  description:
    'Sign in to your Aseda Foods account to access your dashboard and manage your fresh produce subscriptions.',
};

export default async function LoginPage() {
  return (
    <div className='min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8'>
      <div className='max-w-md w-full space-y-8'>
        <div>
          <h2 className='mt-6 text-center text-3xl font-extrabold text-gray-900'>
            Sign in to your account
          </h2>
          <p className='mt-2 text-center text-sm text-gray-600'>
            Or{' '}
            <Link
              href='/signup'
              className='font-medium text-green-600 hover:text-green-500'
            >
              create a new account
            </Link>
          </p>
        </div>

        <LoginForm className='mt-8' />

        <div className='text-center space-y-2'>
          <Link
            href='/forgot-password'
            className='text-sm text-green-600 hover:text-green-500 block'
          >
            Forgot your password?
          </Link>
          <Link href='/' className='text-sm text-gray-600 hover:text-gray-500'>
            ← Back to home
          </Link>
        </div>
      </div>
    </div>
  );
}
