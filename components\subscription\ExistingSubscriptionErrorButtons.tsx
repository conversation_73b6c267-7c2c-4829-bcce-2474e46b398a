'use client';

import { But<PERSON> } from '@/components/ui/button';

export function ExistingSubscriptionErrorButtons() {
  return (
    <div className='gap-3 flex flex-wrap justify-center'>
      <Button
        onClick={() => (window.location.href = '/dashboard')}
        className='w-full sm:w-auto'
      >
        Go to Dashboard
      </Button>
      <Button
        variant='outline'
        onClick={() => window.location.reload()}
        className='w-full sm:w-auto'
      >
        Check Again
      </Button>
    </div>
  );
}
