import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { stripePaymentService } from '@/lib/services/stripe-payment';
import { CreatePaymentIntentData } from '@/lib/types/payment';
import { SubscriptionData } from '@/lib/constants/subscription';

export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { subscriptionData, subscriberId } = body as {
      subscriptionData: SubscriptionData;
      subscriberId: string;
    };

    // Validate required fields
    if (!subscriptionData || !subscriberId) {
      return NextResponse.json(
        { error: 'Missing required fields: subscriptionData and subscriberId' },
        { status: 400 }
      );
    }

    // Verify that the user owns the subscriber record
    const { data: subscriber, error: subscriberError } = await supabase
      .from('subscribers')
      .select('id, user_id')
      .eq('id', subscriberId)
      .eq('user_id', user.id)
      .single();

    if (subscriberError || !subscriber) {
      return NextResponse.json(
        { error: 'Subscriber not found or access denied' },
        { status: 403 }
      );
    }

    // Create payment intent data
    const paymentIntentData: CreatePaymentIntentData = {
      subscriptionData,
      subscriberId,
      amount: 0, // Will be calculated in the service
      currency: 'usd',
      metadata: {
        userId: user.id,
        userEmail: user.email || '',
      },
    };

    // Create payment intent with Stripe
    const { data: paymentIntent, error: paymentError } = await stripePaymentService.createPaymentIntent(paymentIntentData);

    if (paymentError || !paymentIntent) {
      console.error('Payment intent creation failed:', paymentError);
      return NextResponse.json(
        { 
          error: 'Failed to create payment intent',
          details: paymentError?.message 
        },
        { status: 500 }
      );
    }

    // Return the client secret and payment intent details
    return NextResponse.json({
      success: true,
      data: {
        clientSecret: paymentIntent.clientSecret,
        paymentIntentId: paymentIntent.paymentIntentId,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
      },
    });

  } catch (error) {
    console.error('Error in create-intent API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
