import { createClient } from '@/lib/supabase/server';
import { Tables, TablesInsert, TablesUpdate } from '@/lib/supabase/types';
import {
  SubscriptionData,
  calculatePrice,
  getNextDeliveryDate,
  PAYMENT_PLANS,
  type FrequencyId,
  type PaymentPlanId,
} from '@/lib/constants/subscription';

type Subscription = Tables<'subscriptions'>;
type SubscriptionInsert = TablesInsert<'subscriptions'>;
type SubscriptionUpdate = TablesUpdate<'subscriptions'>;
type Subscriber = Tables<'subscribers'>;

export class SubscriptionServerService {
  private async getSupabase() {
    return await createClient();
  }

  /**
   * Check if user has any existing subscription (active or paused)
   */
  async hasExistingSubscription(subscriberId: string): Promise<{
    hasExisting: boolean;
    existingSubscription: Subscription | null;
    error: any;
  }> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('subscriber_id', subscriberId)
        .in('status', ['active', 'paused'])
        .single();

      if (error && error.code !== 'PGRST116') {
        // PGRST116 is "no rows returned"
        return { hasExisting: false, existingSubscription: null, error };
      }

      return {
        hasExisting: !!data,
        existingSubscription: data || null,
        error: null,
      };
    } catch (error) {
      return { hasExisting: false, existingSubscription: null, error };
    }
  }

  /**
   * Check if user has an active subscription (legacy method for backward compatibility)
   */
  async hasActiveSubscription(subscriberId: string): Promise<{
    hasActive: boolean;
    activeSubscription: Subscription | null;
    error: any;
  }> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('subscriber_id', subscriberId)
        .eq('status', 'active')
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        // PGRST116 is "no rows returned"
        return { hasActive: false, activeSubscription: null, error };
      }

      return {
        hasActive: !!data,
        activeSubscription: data || null,
        error: null,
      };
    } catch (error) {
      return { hasActive: false, activeSubscription: null, error };
    }
  }

  /**
   * Create a new subscription with active subscription validation
   */
  async createSubscription(
    subscriberId: string,
    subscriptionData: SubscriptionData
  ): Promise<{ data: Subscription | null; error: any }> {
    try {
      const supabase = await this.getSupabase();

      // Check for existing subscription (active or paused)
      const {
        hasExisting,
        existingSubscription,
        error: checkError,
      } = await this.hasExistingSubscription(subscriberId);

      if (checkError) {
        return { data: null, error: checkError };
      }

      if (hasExisting && existingSubscription) {
        const status = existingSubscription.status || 'active';
        return {
          data: null,
          error: new Error(
            `You already have a ${status} subscription. Please cancel your current subscription before creating a new one. Note: Paused subscriptions also prevent new subscription creation.`
          ),
        };
      }

      const pricing = calculatePrice(
        subscriptionData.boxSize,
        subscriptionData.frequency,
        subscriptionData.paymentPlan
      );

      const nextDeliveryDate = getNextDeliveryDate(subscriptionData.frequency);

      const subscriptionInsert: SubscriptionInsert = {
        subscriber_id: subscriberId,
        box_size: subscriptionData.boxSize,
        frequency: subscriptionData.frequency,
        delivery_type: subscriptionData.deliveryType,
        pickup_location: subscriptionData.pickupLocation,
        payment_plan: subscriptionData.paymentPlan,
        discount_percentage: pricing.totalDiscount,
        deliveries_remaining:
          PAYMENT_PLANS[subscriptionData.paymentPlan].deliveries,
        auto_renew: true,
        next_delivery_date: nextDeliveryDate.toISOString().split('T')[0],
        status: 'active',
        base_price_cents: pricing.basePriceCents,
        is_active: true,
      };

      const { data, error } = await supabase
        .from('subscriptions')
        .insert(subscriptionInsert)
        .select()
        .single();

      if (error) {
        return { data, error };
      }

      // Create initial delivery record for the new subscription
      if (data && data.next_delivery_date) {
        const { error: deliveryError } = await supabase
          .from('deliveries')
          .insert({
            subscription_id: data.id,
            delivery_date: data.next_delivery_date,
            pickup_location: data.pickup_location,
            status: 'scheduled',
          });

        if (deliveryError) {
          console.warn('Failed to create initial delivery:', deliveryError);
          // Don't fail the subscription creation if delivery creation fails
        }
      }

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get user's subscription status summary
   */
  async getUserSubscriptionSummary(userId: string): Promise<{
    data: {
      activeSubscription: Subscription | null;
      pausedSubscriptions: Subscription[];
      cancelledSubscriptions: Subscription[];
      allSubscriptions: Subscription[];
    } | null;
    error: any;
  }> {
    try {
      const supabase = await this.getSupabase();
      const { data: allSubscriptions, error } = await supabase
        .from('subscriptions')
        .select(
          `
          *,
          subscribers!inner(user_id)
        `
        )
        .eq('subscribers.user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        return { data: null, error };
      }

      const activeSubscription =
        allSubscriptions?.find(
          (sub) => sub.status === 'active' && sub.is_active
        ) || null;

      const pausedSubscriptions =
        allSubscriptions?.filter((sub) => sub.status === 'paused') || [];

      const cancelledSubscriptions =
        allSubscriptions?.filter(
          (sub) => sub.status === 'cancelled' || sub.status === 'completed'
        ) || [];

      return {
        data: {
          activeSubscription,
          pausedSubscriptions,
          cancelledSubscriptions,
          allSubscriptions: allSubscriptions || [],
        },
        error: null,
      };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get user's active subscriptions
   */
  async getUserActiveSubscriptions(
    userId: string
  ): Promise<{ data: Subscription[] | null; error: any }> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from('subscriptions')
        .select(
          `
          *,
          subscribers!inner(user_id)
        `
        )
        .eq('subscribers.user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get all user's subscriptions (for subscription history)
   */
  async getUserSubscriptions(
    userId: string
  ): Promise<{ data: Subscription[] | null; error: any }> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from('subscriptions')
        .select(
          `
          *,
          subscribers!inner(user_id),
          payments(*)
        `
        )
        .eq('subscribers.user_id', userId)
        .order('created_at', { ascending: false })
        .order('created_at', { foreignTable: 'payments', ascending: false }); // Order payments by created_at descending

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get subscription by ID
   */
  async getSubscription(
    subscriptionId: string
  ): Promise<{ data: Subscription | null; error: any }> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from('subscriptions')
        .select(
          `
          *,
          subscribers(*)
        `
        )
        .eq('id', subscriptionId)
        .single();

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Update subscription
   */
  async updateSubscription(
    subscriptionId: string,
    updates: SubscriptionUpdate
  ): Promise<{ data: Subscription | null; error: any }> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from('subscriptions')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', subscriptionId)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Pause subscription
   */
  async pauseSubscription(
    subscriptionId: string
  ): Promise<{ data: Subscription | null; error: any }> {
    return this.updateSubscription(subscriptionId, {
      status: 'paused',
      is_active: false,
    });
  }

  /**
   * Resume subscription
   */
  async resumeSubscription(
    subscriptionId: string
  ): Promise<{ data: Subscription | null; error: any }> {
    return this.updateSubscription(subscriptionId, {
      status: 'active',
      is_active: true,
    });
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(
    subscriptionId: string
  ): Promise<{ data: Subscription | null; error: any }> {
    return this.updateSubscription(subscriptionId, {
      status: 'cancelled',
      is_active: false,
      auto_renew: false,
    });
  }

  /**
   * Get subscriber by user ID
   */
  async getSubscriberByUserId(
    userId: string
  ): Promise<{ data: Subscriber | null; error: any }> {
    try {
      const supabase = await this.getSupabase();
      const { data, error } = await supabase
        .from('subscribers')
        .select('*')
        .eq('user_id', userId)
        .single();

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Process delivery (reduce remaining deliveries)
   */
  async processDelivery(
    subscriptionId: string
  ): Promise<{ data: Subscription | null; error: any }> {
    try {
      // Get current subscription
      const { data: subscription, error: fetchError } =
        await this.getSubscription(subscriptionId);

      if (fetchError || !subscription) {
        return {
          data: null,
          error: fetchError || new Error('Subscription not found'),
        };
      }

      const deliveriesRemaining = (subscription.deliveries_remaining || 0) - 1;
      const nextDeliveryDate = getNextDeliveryDate(
        subscription.frequency as FrequencyId
      );

      const updates: SubscriptionUpdate = {
        deliveries_remaining: deliveriesRemaining,
        next_delivery_date: nextDeliveryDate.toISOString().split('T')[0],
      };

      // If no deliveries remaining, mark as completed or renew
      if (deliveriesRemaining <= 0) {
        if (subscription.auto_renew) {
          // Reset deliveries for auto-renewal
          const paymentPlan = subscription.payment_plan as PaymentPlanId;
          updates.deliveries_remaining = PAYMENT_PLANS[paymentPlan].deliveries;
        } else {
          updates.status = 'completed';
          updates.is_active = false;
        }
      }

      return this.updateSubscription(subscriptionId, updates);
    } catch (error) {
      return { data: null, error };
    }
  }

  /**
   * Get all subscriptions for admin
   */
  async getAllSubscriptions(
    status?: string,
    limit = 50,
    offset = 0
  ): Promise<{
    data: Subscription[] | null;
    error: any;
    count: number | null;
  }> {
    try {
      const supabase = await this.getSupabase();
      let query = supabase
        .from('subscriptions')
        .select(
          `
          *,
          subscribers(*)
        `,
          { count: 'exact' }
        )
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (status) {
        query = query.eq('status', status);
      }

      const { data, error, count } = await query;

      return { data, error, count };
    } catch (error) {
      return { data: null, error, count: null };
    }
  }
}

// Export a server instance factory
export const createSubscriptionServerService = () =>
  new SubscriptionServerService();
