import { createClient } from '@/lib/supabase/server';
import {
  Delivery,
  DeliveryInsert,
  DeliveryUpdate,
  CreateDeliveryData,
  UpdateDeliveryData,
  DeliveryWithDetails,
  DeliveryFilters,
  DeliverySummary,
} from '@/lib/types/delivery';
import { AdminResponse, PaginationParams } from '@/lib/types/admin';

export class DeliveryService {
  protected async getSupabase() {
    return await createClient();
  }

  /**
   * Create a new delivery record
   */
  async createDelivery(
    data: CreateDeliveryData
  ): Promise<AdminResponse<Delivery>> {
    try {
      const supabase = await this.getSupabase();

      const deliveryData: DeliveryInsert = {
        subscription_id: data.subscriptionId,
        delivery_date: data.deliveryDate,
        pickup_location: data.pickupLocation,
        box_contents: data.boxContents,
        special_instructions: data.specialInstructions,
        status: 'scheduled',
      };

      const { data: delivery, error } = await supabase
        .from('deliveries')
        .insert(deliveryData)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: delivery, error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to create delivery',
      };
    }
  }

  /**
   * Update delivery status and details
   */
  async updateDelivery(
    deliveryId: string,
    updates: UpdateDeliveryData
  ): Promise<AdminResponse<Delivery>> {
    try {
      const supabase = await this.getSupabase();

      const updateData: DeliveryUpdate = {
        ...(updates.status && { status: updates.status }),
        ...(updates.deliveryDate && { delivery_date: updates.deliveryDate }),
        ...(updates.pickupLocation && {
          pickup_location: updates.pickupLocation,
        }),
        ...(updates.boxContents !== undefined && {
          box_contents: updates.boxContents,
        }),
        ...(updates.specialInstructions !== undefined && {
          special_instructions: updates.specialInstructions,
        }),
      };

      const { data: delivery, error } = await supabase
        .from('deliveries')
        .update(updateData)
        .eq('id', deliveryId)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: delivery, error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to update delivery',
      };
    }
  }

  /**
   * Get delivery by ID with subscription details
   */
  async getDeliveryWithDetails(
    deliveryId: string
  ): Promise<AdminResponse<DeliveryWithDetails>> {
    try {
      const supabase = await this.getSupabase();

      const { data: delivery, error } = await supabase
        .from('deliveries')
        .select(
          `
          *,
          subscriptions (
            id,
            box_size,
            frequency,
            subscribers (
              id,
              name,
              email,
              phone
            )
          )
        `
        )
        .eq('id', deliveryId)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: delivery as DeliveryWithDetails, error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to fetch delivery',
      };
    }
  }

  /**
   * Get paginated deliveries with filters
   */
  async getDeliveries(
    params: PaginationParams,
    filters?: DeliveryFilters
  ): Promise<AdminResponse<{ data: DeliveryWithDetails[]; count: number }>> {
    try {
      const supabase = await this.getSupabase();

      let query = supabase.from('deliveries').select(
        `
          *,
          subscriptions (
            id,
            box_size,
            frequency,
            subscribers (
              id,
              name,
              email,
              phone
            )
          )
        `,
        { count: 'exact' }
      );

      // Apply filters
      if (filters) {
        if (filters.status) {
          query = query.eq('status', filters.status);
        }
        if (filters.pickupLocation) {
          query = query.eq('pickup_location', filters.pickupLocation);
        }
        if (filters.dateRange) {
          query = query
            .gte(
              'delivery_date',
              filters.dateRange.from.toISOString().split('T')[0]
            )
            .lte(
              'delivery_date',
              filters.dateRange.to.toISOString().split('T')[0]
            );
        }
        // Search by subscriber name or email would require a more complex query
        // For now, we'll implement basic filtering
      }

      // Apply sorting
      if (params.sortBy) {
        query = query.order(params.sortBy, {
          ascending: params.sortOrder === 'asc',
        });
      } else {
        query = query.order('delivery_date', { ascending: false });
      }

      // Apply pagination
      const from = (params.page - 1) * params.pageSize;
      const to = from + params.pageSize - 1;

      const { data, error, count } = await query.range(from, to);

      if (error) {
        return { data: null, error: error.message };
      }

      return {
        data: { data: data as DeliveryWithDetails[], count: count || 0 },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to fetch deliveries',
      };
    }
  }

  /**
   * Get delivery summary statistics
   */
  async getDeliverySummary(
    startDate?: Date,
    endDate?: Date
  ): Promise<AdminResponse<DeliverySummary>> {
    try {
      const supabase = await this.getSupabase();

      let query = supabase.from('deliveries').select('status');

      if (startDate && endDate) {
        query = query
          .gte('delivery_date', startDate.toISOString().split('T')[0])
          .lte('delivery_date', endDate.toISOString().split('T')[0]);
      }

      const { data: deliveries, error } = await query;

      if (error) {
        return { data: null, error: error.message };
      }

      const totalDeliveries = deliveries?.length || 0;
      const deliveredCount =
        deliveries?.filter((d) => d.status === 'delivered').length || 0;
      const scheduledCount =
        deliveries?.filter((d) => d.status === 'scheduled').length || 0;
      const cancelledCount =
        deliveries?.filter((d) => d.status === 'cancelled').length || 0;
      const deliveryRate =
        totalDeliveries > 0 ? (deliveredCount / totalDeliveries) * 100 : 0;

      const summary: DeliverySummary = {
        totalDeliveries,
        deliveredCount,
        scheduledCount,
        cancelledCount,
        deliveryRate,
      };

      return { data: summary, error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch delivery summary',
      };
    }
  }

  /**
   * Mark delivery as delivered
   */
  async markAsDelivered(deliveryId: string): Promise<AdminResponse<Delivery>> {
    return this.updateDelivery(deliveryId, { status: 'delivered' });
  }

  /**
   * Cancel delivery
   */
  async cancelDelivery(deliveryId: string): Promise<AdminResponse<Delivery>> {
    return this.updateDelivery(deliveryId, { status: 'cancelled' });
  }

  /**
   * Get deliveries for a specific subscription
   */
  async getDeliveriesForSubscription(
    subscriptionId: string
  ): Promise<AdminResponse<Delivery[]>> {
    try {
      const supabase = await this.getSupabase();

      const { data: deliveries, error } = await supabase
        .from('deliveries')
        .select('*')
        .eq('subscription_id', subscriptionId)
        .order('delivery_date', { ascending: false });

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: deliveries || [], error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to fetch deliveries',
      };
    }
  }
}

// Create a singleton instance
export const createDeliveryService = () => new DeliveryService();
