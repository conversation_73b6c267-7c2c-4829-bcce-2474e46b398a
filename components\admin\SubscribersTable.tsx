'use client';

import { useState, useEffect } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { MoreHorizontal, Edit, Trash2, Plus } from 'lucide-react';
import { toast } from 'sonner';

import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SubscriberWithSubscriptions, Subscriber } from '@/lib/types/admin';
import {
  getSubscribersAction,
  createSubscriberAction,
  updateSubscriberAction,
  deleteSubscriberAction,
} from '@/lib/actions/admin/subscribers';
import { ConfirmDeleteDialog } from './dialogs/ConfirmDeleteDialog';
import { SubscriberFormDialog } from './dialogs/SubscriberFormDialog';

const createColumns = (
  onEdit?: (subscriber: SubscriberWithSubscriptions) => void,
  onDelete?: (subscriber: SubscriberWithSubscriptions) => void
): ColumnDef<SubscriberWithSubscriptions>[] => [
  {
    accessorKey: 'name',
    header: 'Subscriber Name',
    cell: ({ row }) => {
      const subscriber = row.original;
      return (
        <div>
          <div className='font-medium'>{subscriber.name}</div>
          <div className='text-sm text-gray-500'>{subscriber.email}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'phone',
    header: 'Phone',
    cell: ({ row }) => {
      const phone = row.getValue('phone') as string;
      return phone || '-';
    },
  },
  {
    accessorKey: 'address',
    header: 'Address',
    cell: ({ row }) => {
      const address = row.getValue('address') as string;
      return (
        <div className='max-w-xs truncate' title={address}>
          {address}
        </div>
      );
    },
  },
  {
    accessorKey: 'role',
    header: 'Role',
    cell: ({ row }) => {
      const role = row.getValue('role') as string;
      return (
        <Badge variant={role === 'admin' ? 'default' : 'secondary'}>
          {role.charAt(0).toUpperCase() + role.slice(1)}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'subscription_count',
    header: 'Active Subscriptions',
    cell: ({ row }) => {
      const count = row.getValue('subscription_count') as number;
      return (
        <div className='text-right'>
          <Badge variant='outline'>{count}</Badge>
        </div>
      );
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Joined',
    cell: ({ row }) => {
      const date = row.getValue('created_at') as string;
      return format(new Date(date), 'MMM dd, yyyy');
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const subscriber = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(subscriber.email)}
            >
              Copy email
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onEdit?.(subscriber)}>
              <Edit className='mr-2 h-4 w-4' />
              Edit subscriber
            </DropdownMenuItem>
            <DropdownMenuItem
              className='text-red-600'
              onClick={() => onDelete?.(subscriber)}
            >
              <Trash2 className='mr-2 h-4 w-4' />
              Delete subscriber
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

export function SubscribersTable() {
  const [data, setData] = useState<SubscriberWithSubscriptions[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Dialog states
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedSubscriber, setSelectedSubscriber] =
    useState<SubscriberWithSubscriptions | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await getSubscribersAction({
        page: 1,
        pageSize: 1000, // Increased to ensure we get all subscribers
        sortBy: 'created_at',
        sortOrder: 'desc',
      });

      if (!result.success || result.error) {
        setError(result.error || 'Failed to fetch subscribers');
      } else if (result.data) {
        setData(result.data.data);
      } else {
        setData([]);
      }
    } catch (err) {
      setError('Failed to fetch subscribers');
      console.error('Error fetching subscribers:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // CRUD Handlers
  const handleCreate = () => {
    setSelectedSubscriber(null);
    setFormDialogOpen(true);
  };

  const handleEdit = (subscriber: SubscriberWithSubscriptions) => {
    setSelectedSubscriber(subscriber);
    setFormDialogOpen(true);
  };

  const handleDelete = (subscriber: SubscriberWithSubscriptions) => {
    setSelectedSubscriber(subscriber);
    setDeleteDialogOpen(true);
  };

  const handleFormSubmit = async (formData: any) => {
    try {
      // Convert form data to match Subscriber type
      const subscriberData: Omit<Subscriber, 'id' | 'created_at'> = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone || null,
        address: formData.address,
        role: formData.role || null,
        user_id: formData.user_id || null,
        allergies: formData.allergies || null,
        special_instructions: formData.special_instructions || null,
      };

      if (selectedSubscriber) {
        // Update existing subscriber
        const result = await updateSubscriberAction(
          selectedSubscriber.id,
          subscriberData
        );
        if (result.success) {
          toast.success('Subscriber updated successfully');
          fetchData();
        } else {
          toast.error(result.error || 'Failed to update subscriber');
        }
      } else {
        // Create new subscriber
        const result = await createSubscriberAction(subscriberData);
        if (result.success) {
          toast.success('Subscriber created successfully');
          fetchData();
        } else {
          toast.error(result.error || 'Failed to create subscriber');
        }
      }
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error('An unexpected error occurred');
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedSubscriber) return;

    try {
      const result = await deleteSubscriberAction(selectedSubscriber.id);
      if (result.success) {
        toast.success('Subscriber deleted successfully');
        fetchData();
      } else {
        toast.error(result.error || 'Failed to delete subscriber');
      }
    } catch (error) {
      console.error('Delete error:', error);
      toast.error('An unexpected error occurred');
    }
  };

  const columns = createColumns(handleEdit, handleDelete);

  if (loading) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-green-600'></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-center'>
          <p className='text-red-600 mb-2'>Error loading subscribers</p>
          <p className='text-sm text-gray-500'>{error}</p>
          <Button onClick={fetchData} className='mt-2'>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      <div className='flex justify-between items-center'>
        <div>
          <h2 className='text-2xl font-bold'>Subscribers</h2>
          <p className='text-gray-600'>
            Manage customer accounts and subscriptions
          </p>
        </div>
        <Button onClick={handleCreate}>
          <Plus className='mr-2 h-4 w-4' />
          Add Subscriber
        </Button>
      </div>

      <DataTable
        columns={columns}
        data={data}
        searchKey='name'
        searchPlaceholder='Search subscribers...'
        enableRowSelection={true}
        onRowSelectionChange={(selectedRows) => {
          console.log('Selected subscribers:', selectedRows);
        }}
      />

      {/* Dialogs */}
      <SubscriberFormDialog
        open={formDialogOpen}
        onOpenChange={setFormDialogOpen}
        subscriber={selectedSubscriber}
        onSubmit={handleFormSubmit}
      />

      <ConfirmDeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title='Delete Subscriber'
        description='Are you sure you want to delete this subscriber? This action cannot be undone and will cancel all their active subscriptions.'
        itemName={selectedSubscriber?.name}
        onConfirm={handleDeleteConfirm}
      />
    </div>
  );
}
