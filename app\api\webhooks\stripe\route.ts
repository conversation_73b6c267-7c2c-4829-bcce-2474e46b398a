import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { stripe } from '@/lib/stripe/server-config';
import { paymentDatabaseService } from '@/lib/services/payment-database';
import { stripePaymentService } from '@/lib/services/stripe-payment';
import { SubscriptionServerService } from '@/lib/services/subscription-server';
import Stripe from 'stripe';

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(request: NextRequest) {
  if (!webhookSecret) {
    console.error('STRIPE_WEBHOOK_SECRET is not configured');
    return NextResponse.json(
      { error: 'Webhook secret not configured' },
      { status: 500 }
    );
  }

  try {
    const body = await request.text();
    const headersList = await headers();
    const signature = headersList.get('stripe-signature');

    if (!signature) {
      console.error('Missing stripe-signature header');
      return NextResponse.json({ error: 'Missing signature' }, { status: 400 });
    }

    // Verify webhook signature
    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (error) {
      console.error('Webhook signature verification failed:', error);
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.canceled':
        await handlePaymentCanceled(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.requires_action':
        await handlePaymentRequiresAction(
          event.data.object as Stripe.PaymentIntent
        );
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

async function handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log('Payment succeeded:', paymentIntent.id);

    // Update payment status in database
    const paymentStatus = stripePaymentService.mapStripeStatusToPaymentStatus(
      paymentIntent.status
    );
    await paymentDatabaseService.updatePaymentStatus(
      paymentIntent.id,
      paymentStatus
    );

    // If this is the first successful payment, ensure subscription is active
    const { data: payment } =
      await paymentDatabaseService.getPaymentByPaymentId(paymentIntent.id);

    if (payment?.subscription_id) {
      const subscriptionService = new SubscriptionServerService();
      await subscriptionService.updateSubscription(payment.subscription_id, {
        status: 'active',
        is_active: true,
      });
    }

    console.log('Payment success handling completed');
  } catch (error) {
    console.error('Error handling payment succeeded:', error);
  }
}

async function handlePaymentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log('Payment failed:', paymentIntent.id);

    // Update payment status in database
    const paymentStatus = stripePaymentService.mapStripeStatusToPaymentStatus(
      paymentIntent.status
    );
    await paymentDatabaseService.updatePaymentStatus(
      paymentIntent.id,
      paymentStatus
    );

    // Update subscription status to indicate payment failure
    const { data: payment } =
      await paymentDatabaseService.getPaymentByPaymentId(paymentIntent.id);

    if (payment?.subscription_id) {
      const subscriptionService = new SubscriptionServerService();
      await subscriptionService.updateSubscription(payment.subscription_id, {
        status: 'payment_failed',
        is_active: false,
      });
    }

    console.log('Payment failure handling completed');
  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

async function handlePaymentCanceled(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log('Payment canceled:', paymentIntent.id);

    // Update payment status in database
    const paymentStatus = stripePaymentService.mapStripeStatusToPaymentStatus(
      paymentIntent.status
    );
    await paymentDatabaseService.updatePaymentStatus(
      paymentIntent.id,
      paymentStatus
    );

    // Update subscription status
    const { data: payment } =
      await paymentDatabaseService.getPaymentByPaymentId(paymentIntent.id);

    if (payment?.subscription_id) {
      const subscriptionService = new SubscriptionServerService();
      await subscriptionService.updateSubscription(payment.subscription_id, {
        status: 'canceled',
        is_active: false,
      });
    }

    console.log('Payment cancellation handling completed');
  } catch (error) {
    console.error('Error handling payment canceled:', error);
  }
}

async function handlePaymentRequiresAction(
  paymentIntent: Stripe.PaymentIntent
) {
  try {
    console.log('Payment requires action:', paymentIntent.id);

    // Update payment status in database
    const paymentStatus = stripePaymentService.mapStripeStatusToPaymentStatus(
      paymentIntent.status
    );
    await paymentDatabaseService.updatePaymentStatus(
      paymentIntent.id,
      paymentStatus
    );

    console.log('Payment requires action handling completed');
  } catch (error) {
    console.error('Error handling payment requires action:', error);
  }
}
