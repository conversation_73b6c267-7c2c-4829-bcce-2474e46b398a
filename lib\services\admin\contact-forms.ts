// import { createClient } from '@/lib/supabase/server';
import {
  ContactSubmission,
  AdminResponse,
  PaginationParams,
  TableFilters,
} from '@/lib/types/admin';
import { AdminServerService } from '../admin-server';

// Extended contact submission with status
export type ContactSubmissionWithStatus = ContactSubmission & {
  status?: 'new' | 'read' | 'responded';
};

export class ContactFormsAdminService extends AdminServerService {
  /**
   * Get paginated contact forms
   */
  async getContactForms(
    params: PaginationParams,
    filters?: TableFilters
  ): Promise<
    AdminResponse<{ data: ContactSubmissionWithStatus[]; count: number }>
  > {
    try {
      const supabase = await this.getSupabase();

      let query = supabase
        .from('contact_submissions')
        .select('*', { count: 'exact' });

      // Apply search filter (search by name, email, or message)
      if (filters?.search) {
        query = query.or(
          `name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,message.ilike.%${filters.search}%`
        );
      }

      // Apply status filter (if we add a status column later)
      if (filters?.status) {
        // For now, we'll treat all as 'new' since there's no status column yet
        // This can be updated when status column is added to the database
      }

      // Apply date range filter
      if (filters?.dateRange) {
        query = query
          .gte('created_at', filters.dateRange.from.toISOString())
          .lte('created_at', filters.dateRange.to.toISOString());
      }

      // Apply sorting
      if (params.sortBy) {
        query = query.order(params.sortBy, {
          ascending: params.sortOrder === 'asc',
        });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      // Apply pagination
      const from = (params.page - 1) * params.pageSize;
      const to = from + params.pageSize - 1;

      const { data, error, count } = await query.range(from, to);

      if (error) {
        return { data: null, error: error.message };
      }

      // Add default status since it's not in the database yet
      const dataWithStatus: ContactSubmissionWithStatus[] = (data || []).map(
        (item) => ({
          ...item,
          status: 'new' as const, // Default status until we add this column to DB
        })
      );

      return {
        data: { data: dataWithStatus, count: count || 0 },
        error: null,
      };
    } catch (error) {
      console.error('Error fetching contact forms:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch contact forms',
      };
    }
  }

  /**
   * Get single contact form
   */
  async getContactForm(
    id: string
  ): Promise<AdminResponse<ContactSubmissionWithStatus>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('contact_submissions')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      const dataWithStatus: ContactSubmissionWithStatus = {
        ...data,
        status: 'new' as const, // Default status until we add this column to DB
      };

      return { data: dataWithStatus, error: null };
    } catch (error) {
      console.error('Error fetching contact form:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch contact form',
      };
    }
  }

  /**
   * Update contact form (mark as read, add notes, etc.)
   */
  async updateContactForm(
    id: string,
    updates: { status?: 'new' | 'read' | 'responded'; notes?: string }
  ): Promise<AdminResponse<ContactSubmissionWithStatus>> {
    try {
      const supabase = await this.getSupabase();

      // For now, we'll just update the record and return it
      // In the future, we might add status and notes columns to the database
      const { data, error } = await supabase
        .from('contact_submissions')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      // Add status to the response (simulated for now)
      const updatedData: ContactSubmissionWithStatus = {
        ...data,
        status: updates.status || 'read',
      };

      return { data: updatedData, error: null };
    } catch (error) {
      console.error('Error updating contact form:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to update contact form',
      };
    }
  }

  /**
   * Delete contact form
   */
  async deleteContactForm(id: string): Promise<AdminResponse<boolean>> {
    try {
      const supabase = await this.getSupabase();

      const { error } = await supabase
        .from('contact_submissions')
        .delete()
        .eq('id', id);

      if (error) {
        return { data: false, error: error.message };
      }

      return { data: true, error: null };
    } catch (error) {
      console.error('Error deleting contact form:', error);
      return {
        data: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to delete contact form',
      };
    }
  }

  /**
   * Get contact forms for export (all data, no pagination)
   */
  async getContactFormsForExport(
    filters?: TableFilters
  ): Promise<AdminResponse<ContactSubmissionWithStatus[]>> {
    try {
      const supabase = await this.getSupabase();

      let query = supabase.from('contact_submissions').select('*');

      // Apply search filter
      if (filters?.search) {
        query = query.or(
          `name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,message.ilike.%${filters.search}%`
        );
      }

      // Apply date range filter
      if (filters?.dateRange) {
        query = query
          .gte('created_at', filters.dateRange.from.toISOString())
          .lte('created_at', filters.dateRange.to.toISOString());
      }

      const { data, error } = await query.order('created_at', {
        ascending: false,
      });

      if (error) {
        return { data: null, error: error.message };
      }

      // Add default status since it's not in the database yet
      const dataWithStatus: ContactSubmissionWithStatus[] = (data || []).map(
        (item) => ({
          ...item,
          status: 'new' as const,
        })
      );

      return { data: dataWithStatus, error: null };
    } catch (error) {
      console.error('Error fetching contact forms for export:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch contact forms for export',
      };
    }
  }

  /**
   * Get count of pending contact forms (for dashboard stats)
   */
  async getPendingContactFormsCount(): Promise<AdminResponse<number>> {
    try {
      const supabase = await this.getSupabase();

      const { count, error } = await supabase
        .from('contact_submissions')
        .select('*', { count: 'exact', head: true });

      if (error) {
        return { data: 0, error: error.message };
      }

      return { data: count || 0, error: null };
    } catch (error) {
      console.error('Error fetching pending contact forms count:', error);
      return {
        data: 0,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch pending contact forms count',
      };
    }
  }
}

// Create a singleton instance
export const createContactFormsAdminService = () =>
  new ContactFormsAdminService();
