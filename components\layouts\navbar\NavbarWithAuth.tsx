import { Suspense } from 'react';
import { NavbarAuth } from './NavbarAuth';
import { MobileMenuButton } from './MobileMenuButton';
import { NavbarAuthSkeleton } from '@/components/ui/skeletons';
import { PPRErrorBoundary } from '@/components/ui/ppr-error-boundary';

/**
 * Server component that renders desktop auth and mobile menu button
 */
function NavbarWithAuthData() {
  return (
    <>
      {/* Desktop auth section */}
      <NavbarAuth />

      {/* Mobile hamburger button only - menu content will be rendered separately */}
      <MobileMenuButton />
    </>
  );
}

/**
 * Wrapper component that handles Suspense for navbar authentication
 */
export function NavbarWithAuth() {
  return (
    <PPRErrorBoundary>
      <Suspense
        fallback={
          <>
            <NavbarAuthSkeleton />
            <div className='md:hidden'>
              <div className='h-6 w-6 bg-gray-200 rounded animate-pulse'></div>
            </div>
          </>
        }
      >
        <NavbarWithAuthData />
      </Suspense>
    </PPRErrorBoundary>
  );
}
