# Stripe Configuration

This directory contains the Stripe configuration for the AsedaFoods application. The configuration automatically switches between test and live keys based on the environment.

## Overview

The Stripe configuration system consists of:

- **`server-config.ts`** - Server-side Stripe configuration (secret keys, webhooks)
- **`client-config.ts`** - Client-side Stripe configuration (public keys)
- **`../utils/stripe-config.ts`** - Utility functions for environment-aware configuration

## Environment Variables

Make sure you have the following environment variables set in your `.env` file:

### Test Keys (Development)
```env
STRIPE_SECRET_TEST_KEY="sk_test_..."
NEXT_PUBLIC_STRIPE_PUBLIC_TEST_KEY="pk_test_..."
STRIPE_WEBHOOK_SECRET_TEST="whsec_..." # Optional
```

### Live Keys (Production)
```env
STRIPE_SECRET_LIVE_KEY="sk_live_..."
NEXT_PUBLIC_STRIPE_PUBLIC_LIVE_KEY="pk_live_..."
STRIPE_WEBHOOK_SECRET_LIVE="whsec_..." # Optional
STRIPE_WEBHOOK_SECRET="whsec_..." # Fallback for webhook secret
```

## How It Works

The configuration automatically detects the environment and uses the appropriate keys:

- **Development/Test**: Uses `*_TEST_KEY` variables
- **Production**: Uses `*_LIVE_KEY` variables

Environment detection is based on:
- `NODE_ENV === 'development'` or `NODE_ENV === 'test'`
- `NEXT_PUBLIC_ENVIRONMENT === 'development'`

## Usage

### Server-Side (API Routes, Server Components)

```typescript
import { stripe, webhookConfig, stripeServerConfig } from '@/lib/stripe/server-config';

// Use the Stripe instance (automatically configured)
const paymentIntent = await stripe.paymentIntents.create({
  amount: 2000,
  currency: 'usd',
});

// Check current configuration
console.log('Using Stripe in', stripeServerConfig.environment, 'mode');
console.log('Key type:', stripeServerConfig.keyType);

// Use webhook configuration
const webhookSecret = webhookConfig.secret;
```

### Client-Side (Components, Client Code)

```typescript
import { stripeConfig } from '@/lib/stripe/client-config';
import { loadStripe } from '@stripe/stripe-js';

// Initialize Stripe (automatically uses correct public key)
const stripePromise = loadStripe(stripeConfig.publicKey);

// Check current configuration
console.log('Client environment:', stripeConfig.environment);
console.log('Using', stripeConfig.keyType, 'keys');
```

### Direct Utility Usage

```typescript
import stripeConfigUtils from '@/lib/utils/stripe-config';

// Get configuration
const serverConfig = stripeConfigUtils.config.getServerConfig();
const clientConfig = stripeConfigUtils.config.getClientConfig();

// Validate environment variables
const validation = stripeConfigUtils.validation.validateEnvironmentVariables();
if (!validation.isValid) {
  console.error('Missing Stripe keys:', validation.errors);
}

// Get debug information
const debugInfo = stripeConfigUtils.config.getDebugInfo();
console.log('Stripe debug info:', debugInfo);
```

## Environment Detection

The system uses the following logic to determine the environment:

1. **Development Mode**: 
   - `NODE_ENV === 'development'`
   - `NODE_ENV === 'test'`
   - `NEXT_PUBLIC_ENVIRONMENT === 'development'`

2. **Production Mode**: 
   - `NODE_ENV === 'production'` AND `NEXT_PUBLIC_ENVIRONMENT !== 'development'`

## Key Validation

The utility includes validation for:
- Required environment variables are set
- Stripe key format validation (sk_test_/sk_live_ for secret keys, pk_test_/pk_live_ for public keys)

## Debugging

To debug Stripe configuration issues:

```typescript
import stripeConfigUtils from '@/lib/utils/stripe-config';

// Get comprehensive debug information
const debugInfo = stripeConfigUtils.config.getDebugInfo();
console.log('Stripe Configuration Debug:', debugInfo);

// Validate all required variables
const validation = stripeConfigUtils.validation.validateEnvironmentVariables();
if (!validation.isValid) {
  console.error('Configuration errors:', validation.errors);
}
```

## Migration from Previous Setup

If you were previously using hardcoded live keys, the new system will automatically:

1. Use test keys in development (when `NODE_ENV=development`)
2. Use live keys in production (when `NODE_ENV=production`)

No code changes are required in your existing components - they will continue to work with the updated configuration.

## Security Notes

- Test keys are safe to expose in client-side code
- Live secret keys should never be exposed to the client
- Webhook secrets are optional but recommended for production
- All secret keys should be kept secure and not committed to version control

## Troubleshooting

### Common Issues

1. **"Environment variable not set" error**
   - Make sure you have the correct keys in your `.env` file
   - Check that you're using the right key names (TEST vs LIVE)

2. **Wrong keys being used**
   - Check your `NODE_ENV` and `NEXT_PUBLIC_ENVIRONMENT` variables
   - Use the debug utility to see which environment is detected

3. **Webhook issues**
   - Make sure `STRIPE_WEBHOOK_SECRET_TEST` is set for development
   - Use `STRIPE_WEBHOOK_SECRET_LIVE` for production webhooks

### Debug Commands

```bash
# Check environment variables
echo $NODE_ENV
echo $NEXT_PUBLIC_ENVIRONMENT

# Verify Stripe keys are set (don't print the actual values)
node -e "console.log('Test secret:', !!process.env.STRIPE_SECRET_TEST_KEY)"
node -e "console.log('Live secret:', !!process.env.STRIPE_SECRET_LIVE_KEY)"
```
