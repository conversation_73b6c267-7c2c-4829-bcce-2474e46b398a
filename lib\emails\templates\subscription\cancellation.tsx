import {
  Text,
  Section,
  Row,
  Column,
} from '@react-email/components';
import { EmailLayout } from '../../components/EmailLayout';
import { EmailButton } from '../../components/EmailButton';
import { formatDate } from '@/lib/utils/format';

interface SubscriptionCancellationProps {
  subscriberName: string;
  subscriptionData: {
    boxSize: string;
    frequency: string;
    cancellationDate: Date;
    lastDeliveryDate?: Date;
    refundAmount?: number;
  };
}

export function SubscriptionCancellation({
  subscriberName,
  subscriptionData,
}: SubscriptionCancellationProps) {
  const { boxSize, frequency, cancellationDate, lastDeliveryDate, refundAmount } = subscriptionData;

  return (
    <EmailLayout preview="Your subscription has been cancelled">
      <Text
        style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#1f2937',
          margin: '0 0 20px 0',
        }}
      >
        Subscription Cancelled
      </Text>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 0',
        }}
      >
        Hi {subscriberName}, we&apos;re sorry to see you go! Your AsedaFoods CSA subscription has been successfully cancelled.
      </Text>

      <Section
        style={{
          backgroundColor: '#fef2f2',
          padding: '20px',
          borderRadius: '8px',
          margin: '20px 0',
          border: '1px solid #fca5a5',
        }}
      >
        <Text
          style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#dc2626',
            margin: '0 0 15px 0',
          }}
        >
          Cancellation Details
        </Text>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#7f1d1d', margin: '0' }}>
              Box Size:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#dc2626',
                margin: '0',
              }}
            >
              {boxSize}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#7f1d1d', margin: '0' }}>
              Frequency:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#dc2626',
                margin: '0',
              }}
            >
              {frequency}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#7f1d1d', margin: '0' }}>
              Cancellation Date:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#dc2626',
                margin: '0',
              }}
            >
              {formatDate(cancellationDate)}
            </Text>
          </Column>
        </Row>

        {lastDeliveryDate && (
          <Row style={{ marginBottom: '10px' }}>
            <Column style={{ width: '50%' }}>
              <Text style={{ fontSize: '14px', color: '#7f1d1d', margin: '0' }}>
                Last Delivery Date:
              </Text>
            </Column>
            <Column style={{ width: '50%' }}>
              <Text
                style={{
                  fontSize: '14px',
                  fontWeight: 'bold',
                  color: '#dc2626',
                  margin: '0',
                }}
              >
                {formatDate(lastDeliveryDate)}
              </Text>
            </Column>
          </Row>
        )}

        {refundAmount && refundAmount > 0 && (
          <Row>
            <Column style={{ width: '50%' }}>
              <Text style={{ fontSize: '14px', color: '#7f1d1d', margin: '0' }}>
                Refund Amount:
              </Text>
            </Column>
            <Column style={{ width: '50%' }}>
              <Text
                style={{
                  fontSize: '14px',
                  fontWeight: 'bold',
                  color: '#dc2626',
                  margin: '0',
                }}
              >
                ${refundAmount.toFixed(2)}
              </Text>
            </Column>
          </Row>
        )}
      </Section>

      {refundAmount && refundAmount > 0 && (
        <Section
          style={{
            backgroundColor: '#ecfdf5',
            padding: '15px',
            borderRadius: '8px',
            margin: '20px 0',
            border: '1px solid #86efac',
          }}
        >
          <Text
            style={{
              fontSize: '14px',
              color: '#166534',
              margin: '0',
            }}
          >
            <strong>Refund Processing:</strong> Your refund of ${refundAmount.toFixed(2)} will be processed within 3-5 business days and will appear on your original payment method.
          </Text>
        </Section>
      )}

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '20px 0',
        }}
      >
        We&apos;re sad to see you go, but we understand that circumstances change. We hope you enjoyed the fresh, local produce while you were with us.
      </Text>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '20px 0',
        }}
      >
        Your account will remain active, so you can easily restart your subscription anytime you&apos;re ready to rejoin our community.
      </Text>

      <EmailButton
        href="https://asedafoods.org/get-a-box"
        text="Restart Subscription"
        style={{
          backgroundColor: '#16a34a',
        }}
      />

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '30px 0 0 0',
        }}
      >
        <strong>We&apos;d love your feedback!</strong> If you have a moment, please let us know why you cancelled and how we can improve. Your input helps us serve our community better.
      </Text>

      <EmailButton
        href="https://asedafoods.org/contact"
        text="Share Feedback"
        style={{
          backgroundColor: '#6b7280',
          margin: '10px 0',
        }}
      />

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '20px 0 0 0',
        }}
      >
        Thank you for supporting local agriculture during your time with us. We hope to see you again soon!
      </Text>
    </EmailLayout>
  );
}
