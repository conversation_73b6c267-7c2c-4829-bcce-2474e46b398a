import { createClient } from '@/lib/supabase/server';
import { authServer } from '@/lib/utils/auth-server';

/**
 * Server-side subscription services for PPR
 * These services don't need loading states as they're used in server components with Suspense
 */

export async function getUserSubscriptionSummary(userId: string) {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('subscribers')
    .select(`
      id,
      name,
      email,
      subscriptions (
        id,
        box_size,
        frequency,
        pickup_location,
        status,
        deliveries_remaining,
        next_delivery_date,
        auto_renew,
        discount_percentage,
        created_at,
        updated_at
      )
    `)
    .eq('user_id', userId)
    .single();

  if (error) {
    throw new Error(`Failed to fetch subscription summary: ${error.message}`);
  }

  if (!data) {
    throw new Error('Subscriber not found');
  }

  // Process subscriptions by status
  const activeSubscription = data.subscriptions?.find(sub => sub.status === 'active') || null;
  const pausedSubscriptions = data.subscriptions?.filter(sub => sub.status === 'paused') || [];
  const cancelledSubscriptions = data.subscriptions?.filter(sub => sub.status === 'cancelled') || [];

  return {
    subscriber: data,
    activeSubscription,
    pausedSubscriptions,
    cancelledSubscriptions,
    allSubscriptions: data.subscriptions || [],
  };
}

export async function getSubscriberByUserId(userId: string) {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('subscribers')
    .select('*')
    .eq('user_id', userId)
    .single();

  if (error) {
    throw new Error(`Failed to fetch subscriber: ${error.message}`);
  }

  return data;
}

export async function hasExistingSubscription(subscriberId: string) {
  const supabase = await createClient();

  const { data, error } = await supabase
    .from('subscriptions')
    .select('id, status')
    .eq('subscriber_id', subscriberId)
    .in('status', ['active', 'paused'])
    .limit(1);

  if (error) {
    throw new Error(`Failed to check existing subscriptions: ${error.message}`);
  }

  return {
    hasExisting: data && data.length > 0,
    existingSubscription: data?.[0] || null,
  };
}

export async function getCurrentUserSubscriptionSummary() {
  const { data: user } = await authServer.getCurrentUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }

  return getUserSubscriptionSummary(user.id);
}

export async function getCurrentUserSubscriber() {
  const { data: user } = await authServer.getCurrentUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }

  return getSubscriberByUserId(user.id);
}
