'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { authWithToast, handleAuthError } from '@/lib/utils/auth';
import {
  forgotPasswordSchema,
  type ForgotPasswordFormData,
} from '@/lib/validations/auth-schemas';

interface ForgotPasswordFormProps {
  onSuccess?: (email: string) => void;
  className?: string;
}

export default function ForgotPasswordForm({
  onSuccess,
  className,
}: ForgotPasswordFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const form = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = async (data: ForgotPasswordFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      // The toast will handle the promise and show appropriate messages
      await authWithToast.forgotPassword(data.email);

      // If we reach here, the reset email was sent successfully
      setIsSuccess(true);

      // Call success callback if provided
      if (onSuccess) {
        onSuccess(data.email);
      }
    } catch (err) {
      console.error('Forgot password error:', err);
      handleAuthError(err);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTryAgain = () => {
    setIsSuccess(false);
    setError(null);
    form.reset();
  };

  if (isSuccess) {
    return (
      <div className={className}>
        <div className='text-center space-y-4'>
          <div className='mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100'>
            <svg
              className='h-6 w-6 text-green-600'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M5 13l4 4L19 7'
              />
            </svg>
          </div>
          <div>
            <h3 className='text-lg font-medium text-gray-900'>
              Check your email
            </h3>
            <p className='mt-2 text-sm text-gray-600'>
              We&apos;ve sent a password reset link to{' '}
              <span className='font-medium text-gray-900'>
                {form.getValues('email')}
              </span>
            </p>
          </div>
          <div className='text-sm text-gray-600'>
            <p>Didn&apos;t receive the email? Check your spam folder or</p>
            <button
              onClick={handleTryAgain}
              className='font-medium text-green-600 hover:text-green-500'
            >
              try again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
          {error && (
            <div className='bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm'>
              {error}
            </div>
          )}

          <div className='text-center mb-6'>
            <h2 className='text-2xl font-bold text-gray-900'>
              Forgot your password?
            </h2>
            <p className='mt-2 text-sm text-gray-600'>
              Enter your email address and we&apos;ll send you a link to reset
              your password.
            </p>
          </div>

          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email address</FormLabel>
                <FormControl>
                  <Input
                    type='email'
                    placeholder='Enter your email'
                    autoComplete='email'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type='submit' disabled={isLoading} className='w-full'>
            {isLoading ? 'Sending...' : 'Send reset link'}
          </Button>
        </form>
      </Form>
    </div>
  );
}
