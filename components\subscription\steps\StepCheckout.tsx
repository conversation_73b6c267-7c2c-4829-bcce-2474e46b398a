'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CreditCard } from 'lucide-react';
import {
  SubscriptionData,
  BOX_SIZES,
  FREQUENCIES,
  PICKUP_LOCATIONS,
  PAYMENT_PLANS,
  calculatePrice,
  formatPrice,
  getNextDeliveryDate,
} from '@/lib/constants/subscription';
import StripePaymentForm from '@/components/payment/StripePaymentForm';

interface StepCheckoutProps {
  subscriptionData: SubscriptionData;
  subscriberId: string;
  onComplete: (paymentData: any) => void;
  onUpdateData: (updates: Partial<SubscriptionData>) => void;
}

export default function StepCheckout({
  subscriptionData,
  subscriberId,
  onComplete,
  onUpdateData,
}: StepCheckoutProps) {
  const [specialInstructions, setSpecialInstructions] = useState(
    subscriptionData.specialInstructions || ''
  );
  const [showPayment, setShowPayment] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);

  const pricing = calculatePrice(
    subscriptionData.boxSize,
    subscriptionData.frequency,
    subscriptionData.paymentPlan
  );

  const nextDeliveryDate = getNextDeliveryDate(subscriptionData.frequency);

  const handleProceedToPayment = () => {
    // Update subscription data with special instructions
    onUpdateData({
      specialInstructions: specialInstructions.trim(),
    });
    setShowPayment(true);
  };

  const handlePaymentSuccess = (paymentData: any) => {
    setPaymentError(null);
    // Immediately call onComplete to trigger wizard completion UI
    onComplete(paymentData);
  };

  const handlePaymentError = (error: string) => {
    setPaymentError(error);
  };

  return (
    <div className='space-y-6'>
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Pickup Information */}
        <div className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Pickup Information</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <Label>Pickup Location</Label>
                <div className='mt-2 p-3 bg-gray-50 rounded-lg'>
                  <div className='font-medium'>
                    {PICKUP_LOCATIONS[subscriptionData.pickupLocation].name}
                  </div>
                  <div className='text-sm text-gray-600 mt-1'>
                    {PICKUP_LOCATIONS[subscriptionData.pickupLocation].address}
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor='specialInstructions'>
                  Special Instructions (Optional)
                </Label>
                <Textarea
                  id='specialInstructions'
                  placeholder='Any special pickup instructions, dietary restrictions, or preferences...'
                  value={specialInstructions}
                  onChange={(e) => setSpecialInstructions(e.target.value)}
                  rows={3}
                  className='mt-1'
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Order Summary */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              {/* Subscription Details */}
              <div className='space-y-3'>
                <div className='flex justify-between'>
                  <span className='text-sm text-gray-600'>Box Size:</span>
                  <span className='font-medium'>
                    {BOX_SIZES[subscriptionData.boxSize].name}
                  </span>
                </div>

                <div className='flex justify-between'>
                  <span className='text-sm text-gray-600'>Frequency:</span>
                  <span className='font-medium'>
                    {FREQUENCIES[subscriptionData.frequency].name}
                  </span>
                </div>

                <div className='flex justify-between'>
                  <span className='text-sm text-gray-600'>
                    Pickup Location:
                  </span>
                  <span className='font-medium'>
                    {PICKUP_LOCATIONS[subscriptionData.pickupLocation].name}
                  </span>
                </div>

                <div className='flex justify-between'>
                  <span className='text-sm text-gray-600'>Payment Plan:</span>
                  <span className='font-medium'>
                    {PAYMENT_PLANS[subscriptionData.paymentPlan].name}
                  </span>
                </div>

                <div className='flex justify-between'>
                  <span className='text-sm text-gray-600'>Next Pickup:</span>
                  <span className='font-medium'>
                    {nextDeliveryDate.toLocaleDateString()}
                  </span>
                </div>
              </div>

              <Separator />

              {/* Pricing Breakdown */}
              <div className='space-y-2'>
                <div className='flex justify-between'>
                  <span className='text-sm text-gray-600'>
                    Base price per box:
                  </span>
                  <span>{formatPrice(pricing.basePrice)}</span>
                </div>

                {pricing.totalDiscount > 0 && (
                  <div className='flex justify-between text-green-600'>
                    <span className='text-sm'>
                      Discount ({pricing.totalDiscount}%):
                    </span>
                    <span>-{formatPrice(pricing.discountAmount)}</span>
                  </div>
                )}

                <div className='flex justify-between font-medium'>
                  <span className='text-sm'>Price per box:</span>
                  <span>{formatPrice(pricing.pricePerBox)}</span>
                </div>

                <div className='flex justify-between'>
                  <span className='text-sm text-gray-600'>
                    Number of pickups:
                  </span>
                  <span>
                    {PAYMENT_PLANS[subscriptionData.paymentPlan].deliveries}
                  </span>
                </div>
              </div>

              <Separator />

              {/* Total */}
              <div className='space-y-2'>
                <div className='flex justify-between text-lg font-semibold'>
                  <span>Total:</span>
                  <span>{formatPrice(pricing.totalPrice)}</span>
                </div>

                {pricing.savings > 0 && (
                  <div className='flex justify-between text-green-600 font-medium'>
                    <span>You save:</span>
                    <span>{formatPrice(pricing.savings)}</span>
                  </div>
                )}
              </div>

              {/* Discount Badge */}
              {pricing.totalDiscount > 0 && (
                <div className='pt-2'>
                  <Badge className='bg-green-100 text-green-800'>
                    {pricing.totalDiscount}% Total Discount Applied
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Payment Section */}
      {!showPayment ? (
        <Card>
          <CardContent className='pt-6'>
            <div className='space-y-4'>
              <div className='text-sm text-gray-600 space-y-2'>
                <p>
                  <strong>Auto-renewal:</strong> Your subscription will
                  automatically renew after{' '}
                  {PAYMENT_PLANS[subscriptionData.paymentPlan].deliveries}{' '}
                  pickups unless cancelled.
                </p>
                <p>
                  <strong>Cancellation:</strong> You can pause or cancel your
                  subscription at any time from your dashboard.
                </p>
                <p>
                  <strong>Payment:</strong> You will be charged the full amount
                  upfront for your selected payment plan.
                </p>
              </div>

              <Button
                onClick={handleProceedToPayment}
                className='w-full bg-green-600 hover:bg-green-700 text-white py-3'
                size='lg'
              >
                <CreditCard className='mr-2 h-4 w-4' />
                Proceed to Payment - {formatPrice(pricing.totalPrice)}
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {paymentError && (
            <Alert variant='destructive'>
              <AlertDescription>{paymentError}</AlertDescription>
            </Alert>
          )}

          <StripePaymentForm
            subscriptionData={subscriptionData}
            subscriberId={subscriberId}
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
          />
        </>
      )}
    </div>
  );
}
