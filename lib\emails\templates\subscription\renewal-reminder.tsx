import {
  Text,
  Section,
  Row,
  Column,
} from '@react-email/components';
import { EmailLayout } from '../../components/EmailLayout';
import { EmailButton } from '../../components/EmailButton';
import { formatDate, formatPrice } from '@/lib/utils/format';

interface RenewalReminderProps {
  subscriberName: string;
  subscriptionData: {
    boxSize: string;
    frequency: string;
    renewalDate: Date;
    renewalPrice: number;
    deliveriesRemaining: number;
    autoRenew: boolean;
  };
}

export function RenewalReminder({
  subscriberName,
  subscriptionData,
}: RenewalReminderProps) {
  const { 
    boxSize, 
    frequency, 
    renewalDate, 
    renewalPrice, 
    deliveriesRemaining,
    autoRenew 
  } = subscriptionData;

  return (
    <EmailLayout preview="Time to renew your AsedaFoods subscription">
      <Text
        style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#1f2937',
          margin: '0 0 20px 0',
        }}
      >
        Time to Renew Your Subscription! 🌱
      </Text>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 0',
        }}
      >
        Hi {subscriberName}, your AsedaFoods CSA subscription is coming up for renewal soon. 
        We hope you&apos;ve been enjoying your fresh, local produce!
      </Text>

      <Section
        style={{
          backgroundColor: '#fef3c7',
          padding: '20px',
          borderRadius: '8px',
          margin: '20px 0',
          border: '1px solid #fbbf24',
        }}
      >
        <Text
          style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#92400e',
            margin: '0 0 15px 0',
          }}
        >
          Renewal Information
        </Text>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#78350f', margin: '0' }}>
              Current Box Size:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#92400e',
                margin: '0',
              }}
            >
              {boxSize}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#78350f', margin: '0' }}>
              Frequency:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#92400e',
                margin: '0',
              }}
            >
              {frequency}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#78350f', margin: '0' }}>
              Renewal Date:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#92400e',
                margin: '0',
              }}
            >
              {formatDate(renewalDate)}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#78350f', margin: '0' }}>
              Deliveries Remaining:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#92400e',
                margin: '0',
              }}
            >
              {deliveriesRemaining}
            </Text>
          </Column>
        </Row>

        <Row>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#78350f', margin: '0' }}>
              Renewal Price:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#92400e',
                margin: '0',
              }}
            >
              {formatPrice(renewalPrice)}
            </Text>
          </Column>
        </Row>
      </Section>

      {autoRenew ? (
        <Section
          style={{
            backgroundColor: '#dcfce7',
            padding: '15px',
            borderRadius: '8px',
            margin: '20px 0',
            border: '1px solid #16a34a',
          }}
        >
          <Text
            style={{
              fontSize: '14px',
              color: '#166534',
              margin: '0',
            }}
          >
            <strong>Auto-Renewal Enabled:</strong> Your subscription will automatically renew on {formatDate(renewalDate)} 
            for {formatPrice(renewalPrice)}. No action needed unless you want to make changes!
          </Text>
        </Section>
      ) : (
        <Section
          style={{
            backgroundColor: '#fef2f2',
            padding: '15px',
            borderRadius: '8px',
            margin: '20px 0',
            border: '1px solid #fca5a5',
          }}
        >
          <Text
            style={{
              fontSize: '14px',
              color: '#7f1d1d',
              margin: '0',
            }}
          >
            <strong>Action Required:</strong> Auto-renewal is disabled. You&apos;ll need to manually renew your subscription 
            before {formatDate(renewalDate)} to continue receiving fresh produce.
          </Text>
        </Section>
      )}

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '20px 0',
        }}
      >
        Want to make changes before renewal? You can:
      </Text>

      <ul
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 20px',
          paddingLeft: '0',
        }}
      >
        <li style={{ marginBottom: '8px' }}>
          Change your box size or delivery frequency
        </li>
        <li style={{ marginBottom: '8px' }}>
          Update your pickup location
        </li>
        <li style={{ marginBottom: '8px' }}>
          Modify your &quot;never send&quot; preferences
        </li>
        <li style={{ marginBottom: '8px' }}>
          {autoRenew ? 'Disable auto-renewal' : 'Enable auto-renewal for convenience'}
        </li>
        <li style={{ marginBottom: '8px' }}>
          Pause or cancel your subscription
        </li>
      </ul>

      <EmailButton 
        href="https://asedafoods.org/dashboard" 
        text="Manage Subscription" 
      />

      {!autoRenew && (
        <EmailButton
          href="https://asedafoods.org/get-a-box"
          text="Renew Now"
          style={{
            backgroundColor: '#dc2626',
            margin: '10px 0',
          }}
        />
      )}

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '30px 0 20px 0',
        }}
      >
        <strong>Why continue with AsedaFoods?</strong>
      </Text>

      <ul
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 20px',
          paddingLeft: '0',
        }}
      >
        <li style={{ marginBottom: '8px' }}>
          Support local farmers and sustainable agriculture
        </li>
        <li style={{ marginBottom: '8px' }}>
          Enjoy the freshest seasonal produce at peak flavor
        </li>
        <li style={{ marginBottom: '8px' }}>
          Discover new vegetables and expand your culinary horizons
        </li>
        <li style={{ marginBottom: '8px' }}>
          Convenient pickup locations near you
        </li>
        <li style={{ marginBottom: '8px' }}>
          Reduce your environmental impact with local food
        </li>
      </ul>

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '20px 0 0 0',
        }}
      >
        Questions about renewal or want to discuss your subscription? 
        We&apos;re here to help make your CSA experience perfect for you.
      </Text>

      <EmailButton
        href="https://asedafoods.org/contact"
        text="Contact Us"
        style={{
          backgroundColor: '#6b7280',
          margin: '10px 0',
        }}
      />

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '20px 0 0 0',
        }}
      >
        Thank you for being part of the AsedaFoods community and supporting local agriculture!
      </Text>
    </EmailLayout>
  );
}
