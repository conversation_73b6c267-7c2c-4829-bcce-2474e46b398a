'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Line,
  LineChart,
  Pie,
  PieChart,
  Cell,
  XAxis,
  YAxis,
} from 'recharts';
import { getChartDataAction } from '@/lib/actions/admin/charts';

// Types for chart data
type ChartData = {
  subscriberGrowth: Array<{ month: string; subscribers: number }>;
  subscriptionStatus: Array<{ status: string; count: number; fill: string }>;
  deliveryMetrics: Array<{
    week: string;
    delivered: number;
    scheduled: number;
  }>;
  contactForms: Array<{ month: string; submissions: number }>;
};

const subscriberGrowthConfig: ChartConfig = {
  subscribers: {
    label: 'Subscribers',
    color: '#22c55e',
  },
};

const deliveryMetricsConfig: ChartConfig = {
  delivered: {
    label: 'Delivered',
    color: '#22c55e',
  },
  scheduled: {
    label: 'Scheduled',
    color: '#3b82f6',
  },
};

const contactFormsConfig: ChartConfig = {
  submissions: {
    label: 'Submissions',
    color: '#8b5cf6',
  },
};

export function AdminCharts() {
  const [chartData, setChartData] = useState<ChartData>({
    subscriberGrowth: [],
    subscriptionStatus: [],
    deliveryMetrics: [],
    contactForms: [],
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchChartData = async () => {
      setLoading(true);
      setError(null);

      try {
        const result = await getChartDataAction();

        if (!result.success || result.error) {
          setError(result.error || 'Failed to fetch chart data');
        } else if (result.data) {
          setChartData(result.data);
        }
      } catch {
        setError('Failed to fetch chart data');
      } finally {
        setLoading(false);
      }
    };

    fetchChartData();
  }, []);

  if (loading) {
    return (
      <div className='grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6'>
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className='h-4 md:h-6 bg-gray-200 rounded animate-pulse' />
            </CardHeader>
            <CardContent>
              <div className='h-[150px] md:h-[200px] bg-gray-100 rounded animate-pulse' />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className='grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6'>
        <Card className='md:col-span-2'>
          <CardContent className='flex items-center justify-center h-[150px] md:h-[200px]'>
            <div className='text-center px-4'>
              <p className='text-red-600 font-medium text-sm md:text-base'>
                Error loading charts
              </p>
              <p className='text-xs md:text-sm text-gray-500 mt-1'>{error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6'>
      {/* Subscriber Growth Chart */}
      <Card>
        <CardHeader className='pb-2 md:pb-4'>
          <CardTitle className='text-sm md:text-base'>
            Subscriber Growth
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={subscriberGrowthConfig}
            className='h-[150px] md:h-[200px]'
          >
            <AreaChart data={chartData.subscriberGrowth}>
              <CartesianGrid strokeDasharray='3 3' />
              <XAxis
                dataKey='month'
                fontSize={12}
                tickMargin={8}
                className='text-xs md:text-sm'
              />
              <YAxis
                fontSize={12}
                tickMargin={8}
                className='text-xs md:text-sm'
              />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Area
                type='monotone'
                dataKey='subscribers'
                stroke='var(--color-subscribers)'
                fill='var(--color-subscribers)'
                fillOpacity={0.3}
              />
            </AreaChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Subscription Status Distribution */}
      <Card>
        <CardHeader className='pb-2 md:pb-4'>
          <CardTitle className='text-sm md:text-base'>
            Subscription Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='h-[150px] md:h-[200px] flex items-center justify-center'>
            <div className='w-[150px] h-[150px] md:w-[200px] md:h-[200px]'>
              <PieChart width={150} height={150} className='md:hidden'>
                <Pie
                  data={chartData.subscriptionStatus}
                  cx={75}
                  cy={75}
                  innerRadius={30}
                  outerRadius={60}
                  dataKey='count'
                >
                  {chartData.subscriptionStatus.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                </Pie>
                <ChartTooltip
                  content={({ active, payload }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload;
                      return (
                        <div className='bg-white p-2 border rounded shadow text-xs'>
                          <p className='font-medium'>{data.status}</p>
                          <p className='text-gray-600'>
                            {data.count} subscriptions
                          </p>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
              </PieChart>
              <PieChart width={200} height={200} className='hidden md:block'>
                <Pie
                  data={chartData.subscriptionStatus}
                  cx={100}
                  cy={100}
                  innerRadius={40}
                  outerRadius={80}
                  dataKey='count'
                >
                  {chartData.subscriptionStatus.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                </Pie>
                <ChartTooltip
                  content={({ active, payload }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload;
                      return (
                        <div className='bg-white p-2 border rounded shadow text-sm'>
                          <p className='font-medium'>{data.status}</p>
                          <p className='text-gray-600'>
                            {data.count} subscriptions
                          </p>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
              </PieChart>
            </div>
          </div>
          <div className='flex flex-wrap justify-center gap-2 md:gap-4 mt-2 md:mt-4'>
            {chartData.subscriptionStatus.map((item) => (
              <div
                key={item.status}
                className='flex items-center gap-1 md:gap-2'
              >
                <div
                  className='w-2 h-2 md:w-3 md:h-3 rounded-full'
                  style={{ backgroundColor: item.fill }}
                />
                <span className='text-xs md:text-sm'>
                  {item.status}: {item.count}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Delivery Metrics */}
      <Card>
        <CardHeader className='pb-2 md:pb-4'>
          <CardTitle className='text-sm md:text-base'>
            Weekly Delivery Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={deliveryMetricsConfig}
            className='h-[150px] md:h-[200px]'
          >
            <BarChart data={chartData.deliveryMetrics}>
              <CartesianGrid strokeDasharray='3 3' />
              <XAxis
                dataKey='week'
                fontSize={12}
                tickMargin={8}
                className='text-xs md:text-sm'
              />
              <YAxis
                fontSize={12}
                tickMargin={8}
                className='text-xs md:text-sm'
              />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Bar dataKey='scheduled' fill='var(--color-scheduled)' />
              <Bar dataKey='delivered' fill='var(--color-delivered)' />
            </BarChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Contact Form Trends */}
      <Card>
        <CardHeader className='pb-2 md:pb-4'>
          <CardTitle className='text-sm md:text-base'>
            Contact Form Submissions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={contactFormsConfig}
            className='h-[150px] md:h-[200px]'
          >
            <LineChart data={chartData.contactForms}>
              <CartesianGrid strokeDasharray='3 3' />
              <XAxis
                dataKey='month'
                fontSize={12}
                tickMargin={8}
                className='text-xs md:text-sm'
              />
              <YAxis
                fontSize={12}
                tickMargin={8}
                className='text-xs md:text-sm'
              />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Line
                type='monotone'
                dataKey='submissions'
                stroke='var(--color-submissions)'
                strokeWidth={2}
              />
            </LineChart>
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  );
}
