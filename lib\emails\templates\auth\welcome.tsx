import { Text } from '@react-email/components';
import { EmailLayout } from '../../components/EmailLayout';
import { EmailButton } from '../../components/EmailButton';

interface WelcomeEmailProps {
  name: string;
}

export function WelcomeEmail({ name }: WelcomeEmailProps) {
  return (
    <EmailLayout preview='Welcome to AsedaFoods CSA! Your account is ready.'>
      <Text
        style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#1f2937',
          margin: '0 0 20px 0',
        }}
      >
        Welcome to AsedaFoods, {name}!
      </Text>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 0',
        }}
      >
        Thank you for creating your account with AsedaFoods! We&apos;re excited
        to have you join our community of local food supporters who are
        passionate about fresh, seasonal produce from local farms.
      </Text>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '20px 0',
        }}
      >
        Your account is now active and ready to use. You can:
      </Text>

      <ul
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 20px',
          paddingLeft: '0',
        }}
      >
        <li style={{ marginBottom: '8px' }}>
          Browse our subscription options and choose the perfect box size for
          your household
        </li>
        <li style={{ marginBottom: '8px' }}>
          Manage your subscription preferences and delivery schedule
        </li>
      </ul>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '20px 0',
        }}
      >
        Ready to start your fresh food journey? Explore our subscription options
        and get farm-fresh produce delivered to convenient pickup locations.
      </Text>

      <EmailButton
        href='https://asedafoods.org/get-a-box'
        text='Start Your Subscription'
      />

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '20px 0',
        }}
      >
        You can also access your dashboard anytime to manage your account and
        view your subscription status.
      </Text>

      <EmailButton
        href='https://asedafoods.org/dashboard'
        text='Go to Dashboard'
        style={{
          backgroundColor: '#6b7280',
          margin: '10px 0',
        }}
      />

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '30px 0 0 0',
        }}
      >
        Questions about getting started? Just reply to this email or visit our{' '}
        <a href='https://asedafoods.org/contact' style={{ color: '#16a34a' }}>
          contact page
        </a>
        . We&apos;re here to help make your CSA experience amazing!
      </Text>

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '20px 0 0 0',
        }}
      >
        Thank you for supporting local agriculture and sustainable farming
        practices. Together, we&apos;re building a stronger, healthier
        community!
      </Text>
    </EmailLayout>
  );
}
