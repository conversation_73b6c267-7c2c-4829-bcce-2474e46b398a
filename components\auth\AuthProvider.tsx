'use client';

import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from 'react';
import { createClient } from '@/lib/supabase/client';
import { type AuthUser } from '@/lib/utils/auth';
import { useUserStore } from '@/lib/store/users';
import { toast } from 'sonner';

export type SessionState =
  | 'loading'
  | 'authenticated'
  | 'unauthenticated'
  | 'error';

interface SessionInfo {
  user: AuthUser | null;
  state: SessionState;
  isValid: boolean;
  expiresAt: Date | null;
  lastValidated: Date | null;
  error?: string;
}

interface AuthContextType {
  user: AuthUser | null;
  loading: boolean;
  sessionState: SessionState;
  isSessionValid: boolean;
  sessionError?: string;
  expiresAt: Date | null;
  lastValidated: Date | null;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<void>;
  validateSession: () => Promise<boolean>;
  isAuthenticated: boolean;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
  initialUser?: AuthUser | null;
}

export default function AuthProvider({
  children,
  initialUser,
}: AuthProviderProps) {
  const supabase = createClient();
  const { updateUser, removeUser } = useUserStore();

  const [sessionInfo, setSessionInfo] = useState<SessionInfo>({
    user: initialUser || null,
    state: initialUser ? 'authenticated' : 'loading',
    isValid: !!initialUser,
    expiresAt: null,
    lastValidated: initialUser ? new Date() : null,
  });

  const [refreshTimer, setRefreshTimer] = useState<NodeJS.Timeout | null>(null);

  // Get user role from subscribers table
  const getUserWithRole = useCallback(
    async (user: any): Promise<AuthUser | null> => {
      try {
        if (!user) return null;

        // Get user role from subscribers table
        const { data: subscriber, error: subscriberError } = await supabase
          .from('subscribers')
          .select('role, name')
          .eq('user_id', user.id)
          .single();

        if (subscriberError) {
          // Handle specific error cases gracefully
          if (subscriberError.code === 'PGRST116') {
            console.log('Subscriber record not found, using default role');
          } else if (subscriberError.code === '42P17') {
            console.warn('RLS recursion detected, using default role');
          } else {
            console.error('Error fetching subscriber data:', subscriberError);
          }
        }

        const authUser: AuthUser = {
          id: user.id,
          email: user.email!,
          name: subscriber?.name || user.user_metadata?.name || '',
          role: subscriber?.role || 'user',
        };

        return authUser;
      } catch (error) {
        console.error('Failed to get user with role:', error);
        return null;
      }
    },
    [supabase]
  );

  // Validate current session
  const validateSession = useCallback(async (): Promise<boolean> => {
    try {
      setSessionInfo((prev) => ({ ...prev, state: 'loading' }));

      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();

      if (sessionError) {
        console.error('Session validation error:', sessionError);
        setSessionInfo({
          user: null,
          state: 'error',
          isValid: false,
          expiresAt: null,
          lastValidated: new Date(),
          error: sessionError.message,
        });
        return false;
      }

      if (!session || !session.user) {
        setSessionInfo({
          user: null,
          state: 'unauthenticated',
          isValid: false,
          expiresAt: null,
          lastValidated: new Date(),
        });
        removeUser(null);
        return false;
      }

      // Check if session is expired
      const expiresAt = new Date(session.expires_at! * 1000);
      const isExpired = expiresAt <= new Date();

      if (isExpired) {
        console.warn('Session expired');
        setSessionInfo({
          user: null,
          state: 'unauthenticated',
          isValid: false,
          expiresAt,
          lastValidated: new Date(),
          error: 'Session expired',
        });
        removeUser(null);
        return false;
      }

      // Get user data with role
      const authUser = await getUserWithRole(session.user);

      if (!authUser) {
        setSessionInfo({
          user: null,
          state: 'error',
          isValid: false,
          expiresAt,
          lastValidated: new Date(),
          error: 'Failed to load user data',
        });
        return false;
      }

      setSessionInfo({
        user: authUser,
        state: 'authenticated',
        isValid: true,
        expiresAt,
        lastValidated: new Date(),
        error: undefined,
      });

      // Update store
      updateUser(session.user);
      return true;
    } catch (error) {
      console.error('Session validation failed:', error);
      setSessionInfo({
        user: null,
        state: 'error',
        isValid: false,
        expiresAt: null,
        lastValidated: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return false;
    }
  }, [supabase, getUserWithRole, updateUser, removeUser]);

  // Refresh session
  const refreshSession = useCallback(async (): Promise<void> => {
    console.log('Refreshing session');
    await validateSession();
  }, [validateSession]);

  // Sign out
  const signOut = useCallback(async (): Promise<void> => {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('Sign out error:', error);
        toast.error('Failed to sign out');
        return;
      }

      setSessionInfo({
        user: null,
        state: 'unauthenticated',
        isValid: false,
        expiresAt: null,
        lastValidated: new Date(),
      });

      removeUser(null);
      toast.success('Signed out successfully');
    } catch (error) {
      console.error('Sign out failed:', error);
      toast.error('Failed to sign out');
    }
  }, [supabase, removeUser]);

  // Auto-refresh setup
  const startAutoRefresh = useCallback(() => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
    }

    const timer = setInterval(
      async () => {
        // Check current session state directly instead of relying on dependency
        setSessionInfo((current) => {
          if (current.state === 'authenticated') {
            console.log('Auto-refreshing session');
            validateSession();
          }
          return current;
        });
      },
      5 * 60 * 1000
    ); // 5 minutes

    setRefreshTimer(timer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [validateSession]);

  // Window focus handler
  const handleWindowFocus = useCallback(async () => {
    // Check current session state directly to avoid dependency issues
    setSessionInfo((current) => {
      if (current.state === 'authenticated') {
        console.log('Window focused, validating session');
        validateSession();
      }
      return current;
    });
  }, [validateSession]);

  useEffect(() => {
    // Initial session validation
    if (!initialUser) {
      validateSession();
    }

    // Set up auth state listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state change:', event, session?.user?.id);

      switch (event) {
        case 'SIGNED_IN':
          if (session?.user) {
            const authUser = await getUserWithRole(session.user);
            if (authUser) {
              setSessionInfo({
                user: authUser,
                state: 'authenticated',
                isValid: true,
                expiresAt: new Date(session.expires_at! * 1000),
                lastValidated: new Date(),
              });
              updateUser(session.user);
            }
          }
          break;
        case 'SIGNED_OUT':
          setSessionInfo({
            user: null,
            state: 'unauthenticated',
            isValid: false,
            expiresAt: null,
            lastValidated: new Date(),
          });
          removeUser(null);
          break;
        case 'TOKEN_REFRESHED':
          if (session?.user) {
            await validateSession();
          }
          break;
        case 'USER_UPDATED':
          if (session?.user) {
            await validateSession();
          }
          break;
        default:
          break;
      }
    });

    // Set up window focus listener
    window.addEventListener('focus', handleWindowFocus);

    // Start auto-refresh
    startAutoRefresh();

    return () => {
      subscription.unsubscribe();
      window.removeEventListener('focus', handleWindowFocus);
      if (refreshTimer) {
        clearInterval(refreshTimer);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    initialUser,
    supabase.auth,
    getUserWithRole,
    updateUser,
    removeUser,
    validateSession,
    handleWindowFocus,
    startAutoRefresh,
  ]);

  const value: AuthContextType = {
    user: sessionInfo.user,
    loading: sessionInfo.state === 'loading',
    sessionState: sessionInfo.state,
    isSessionValid: sessionInfo.isValid,
    sessionError: sessionInfo.error,
    expiresAt: sessionInfo.expiresAt,
    lastValidated: sessionInfo.lastValidated,
    signOut,
    refreshSession,
    validateSession,
    isAuthenticated:
      sessionInfo.state === 'authenticated' && !!sessionInfo.user,
    isAdmin: sessionInfo.user?.role === 'admin',
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
