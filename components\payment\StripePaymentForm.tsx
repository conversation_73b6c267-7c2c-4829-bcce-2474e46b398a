'use client';

import { useState, useEffect, useCallback } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CreditCard } from 'lucide-react';
import { stripeConfig } from '@/lib/stripe/client-config';
import { SubscriptionData } from '@/lib/constants/subscription';

// Initialize Stripe
const stripePromise = loadStripe(stripeConfig.publicKey);

interface StripePaymentFormProps {
  subscriptionData: SubscriptionData;
  subscriberId: string;
  onSuccess: (data: any) => void;
  onError: (error: string) => void;
}

interface PaymentFormProps {
  subscriptionData: SubscriptionData;
  subscriberId: string;
  onSuccess: (data: any) => void;
  onError: (error: string) => void;
}

// Payment form component that uses Stripe Elements
function PaymentForm({
  subscriptionData,
  subscriberId,
  onSuccess,
  onError,
}: PaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);
    setPaymentError(null);

    try {
      // Confirm payment with Stripe
      const { error: stripeError, paymentIntent } = await stripe.confirmPayment(
        {
          elements,
          redirect: 'if_required',
        }
      );

      if (stripeError) {
        const errorMessage = stripeError.message || 'Payment failed';
        setPaymentError(errorMessage);
        onError(errorMessage);
        setIsProcessing(false);
        return;
      }

      if (paymentIntent && paymentIntent.status === 'succeeded') {
        // Confirm payment with our backend
        const response = await fetch('/api/payments/confirm', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            paymentIntentId: paymentIntent.id,
            subscriptionData,
            subscriberId,
          }),
        });

        const result = await response.json();

        if (result.success) {
          onSuccess(result.data);
        } else {
          const errorMessage = result.error || 'Failed to create subscription';
          setPaymentError(errorMessage);
          onError(errorMessage);
        }
      } else {
        const errorMessage = 'Payment was not completed successfully';
        setPaymentError(errorMessage);
        onError(errorMessage);
      }
    } catch (error) {
      console.error('Payment error:', error);
      const errorMessage = 'An unexpected error occurred during payment';
      setPaymentError(errorMessage);
      onError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className='space-y-6'>
      <div className='space-y-4'>
        <PaymentElement
          options={{
            layout: 'tabs',
            paymentMethodOrder: ['card'],
          }}
        />
      </div>

      {paymentError && (
        <Alert variant='destructive'>
          <AlertDescription>{paymentError}</AlertDescription>
        </Alert>
      )}

      <Button
        type='submit'
        disabled={!stripe || !elements || isProcessing}
        className='w-full bg-green-600 hover:bg-green-700 text-white py-3'
        size='lg'
      >
        {isProcessing ? (
          <>
            <Loader2 className='mr-2 h-4 w-4 animate-spin' />
            Processing Payment...
          </>
        ) : (
          <>
            <CreditCard className='mr-2 h-4 w-4' />
            Complete Payment
          </>
        )}
      </Button>
    </form>
  );
}

// Main component that handles payment intent creation
export default function StripePaymentForm({
  subscriptionData,
  subscriberId,
  onSuccess,
  onError,
}: StripePaymentFormProps) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const createPaymentIntent = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/payments/create-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionData,
          subscriberId,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setClientSecret(result.data.clientSecret);
      } else {
        setError(result.error || 'Failed to initialize payment');
        onError(result.error || 'Failed to initialize payment');
      }
    } catch (error) {
      console.error('Error creating payment intent:', error);
      const errorMessage = 'Failed to initialize payment';
      setError(errorMessage);
      onError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [subscriptionData, subscriberId, onError]);

  useEffect(() => {
    createPaymentIntent();
  }, [createPaymentIntent]);

  if (isLoading) {
    return (
      <Card>
        <CardContent className='pt-6'>
          <div className='flex items-center justify-center py-8'>
            <Loader2 className='h-8 w-8 animate-spin text-green-600' />
            <span className='ml-2 text-gray-600'>Initializing payment...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className='pt-6'>
          <Alert variant='destructive'>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button
            onClick={createPaymentIntent}
            className='w-full mt-4'
            variant='outline'
          >
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!clientSecret) {
    return (
      <Card>
        <CardContent className='pt-6'>
          <Alert>
            <AlertDescription>Payment initialization failed</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const stripeOptions = {
    clientSecret,
    appearance: {
      theme: 'stripe' as const,
      variables: {
        colorPrimary: '#16a34a',
        colorBackground: '#ffffff',
        colorText: '#1f2937',
        colorDanger: '#dc2626',
        fontFamily: 'system-ui, sans-serif',
        spacingUnit: '4px',
        borderRadius: '6px',
      },
    },
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center'>
          <CreditCard className='mr-2 h-5 w-5' />
          Payment Information
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Elements stripe={stripePromise} options={stripeOptions}>
          <PaymentForm
            subscriptionData={subscriptionData}
            subscriberId={subscriberId}
            onSuccess={onSuccess}
            onError={onError}
          />
        </Elements>
      </CardContent>
    </Card>
  );
}
