'use client';

import { useState, useEffect } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { MoreHorizontal, Edit, Trash2, Copy, Plus } from 'lucide-react';
import { toast } from 'sonner';

import { DataTable } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { BoxContent } from '@/lib/types/admin';
import {
  getBoxContentsAction,
  deleteBoxContentAction,
} from '@/lib/actions/admin/box-contents';
import { ConfirmDeleteDialog } from './dialogs/ConfirmDeleteDialog';

const truncateText = (text: string, maxLength: number = 80) => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

const createColumns = (
  onEdit?: (boxContent: BoxContent) => void,
  onDelete?: (boxContent: BoxContent) => void,
  onDuplicate?: (boxContent: BoxContent) => void
): ColumnDef<BoxContent>[] => [
  {
    accessorKey: 'week_start_date',
    header: 'Week Starting',
    cell: ({ row }) => {
      const date = row.getValue('week_start_date') as string;
      return (
        <div className='font-medium'>
          {format(new Date(date), 'MMM dd, yyyy')}
        </div>
      );
    },
  },
  {
    accessorKey: 'contents',
    header: 'Box Contents',
    cell: ({ row }) => {
      const contents = row.getValue('contents') as string;
      return (
        <div className='max-w-md'>
          <p className='text-sm' title={contents}>
            {truncateText(contents)}
          </p>
        </div>
      );
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created',
    cell: ({ row }) => {
      const date = row.getValue('created_at') as string;
      return format(new Date(date), 'MMM dd, yyyy');
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const boxContent = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => onEdit?.(boxContent)}>
              <Edit className='mr-2 h-4 w-4' />
              Edit content
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onDuplicate?.(boxContent)}>
              <Copy className='mr-2 h-4 w-4' />
              Duplicate for next week
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className='text-red-600'
              onClick={() => onDelete?.(boxContent)}
            >
              <Trash2 className='mr-2 h-4 w-4' />
              Delete content
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

export function BoxContentsTable() {
  const [data, setData] = useState<BoxContent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Dialog states
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedBoxContent, setSelectedBoxContent] =
    useState<BoxContent | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await getBoxContentsAction({
        page: 1,
        pageSize: 100,
        sortBy: 'week_start_date',
        sortOrder: 'desc',
      });

      if (!result.success || result.error) {
        setError(result.error || 'Failed to fetch box contents');
      } else if (result.data) {
        setData(result.data.data);
      }
    } catch {
      setError('Failed to fetch box contents');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // CRUD Handlers
  const handleEdit = (boxContent: BoxContent) => {
    // Navigate to edit page
    window.location.href = `/admin/box-contents/edit/${boxContent.id}`;
  };

  const handleDuplicate = async () => {
    // For now, just show a toast - this could be implemented later
    toast.info('Duplicate functionality coming soon');
  };

  const handleDelete = (boxContent: BoxContent) => {
    setSelectedBoxContent(boxContent);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedBoxContent) return;

    try {
      const result = await deleteBoxContentAction(selectedBoxContent.id);
      if (result.success) {
        toast.success('Box content deleted successfully');
        fetchData();
      } else {
        toast.error(result.error || 'Failed to delete box content');
      }
    } catch {
      toast.error('An unexpected error occurred');
    }
  };

  const handleCreate = () => {
    window.location.href = '/admin/box-contents/create';
  };

  const columns = createColumns(handleEdit, handleDelete, handleDuplicate);

  if (loading) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-green-600'></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-center'>
          <p className='text-red-600 mb-2'>Error loading box contents</p>
          <p className='text-sm text-gray-500'>{error}</p>
          <Button onClick={fetchData} className='mt-2'>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      <div className='flex justify-between items-center'>
        <div>
          <h2 className='text-2xl font-bold'>Box Contents</h2>
          <p className='text-gray-600'>Manage weekly produce box contents</p>
        </div>
        <Button onClick={handleCreate}>
          <Plus className='mr-2 h-4 w-4' />
          Add Box Content
        </Button>
      </div>

      <DataTable
        columns={columns}
        data={data}
        searchKey='contents'
        searchPlaceholder='Search box contents...'
        enableRowSelection={true}
        onRowSelectionChange={(selectedRows) => {
          console.log('Selected box contents:', selectedRows);
        }}
      />

      {/* Dialogs */}
      <ConfirmDeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title='Delete Box Content'
        description='Are you sure you want to delete this box content? This action cannot be undone.'
        itemName={
          selectedBoxContent
            ? `Week of ${format(new Date(selectedBoxContent.week_start_date), 'MMM dd, yyyy')}`
            : undefined
        }
        onConfirm={handleDeleteConfirm}
      />
    </div>
  );
}
