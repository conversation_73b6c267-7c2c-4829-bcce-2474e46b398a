'use client';

import { MobileMenuContent } from './MobileMenuContent';

interface MobileMenuWrapperProps {
  user?: {
    id: string;
    email: string;
    name?: string;
    role?: string;
  } | null;
}

/**
 * Client wrapper component that receives user data from server
 * and renders the mobile menu content
 */
export function MobileMenuWrapper({ user }: MobileMenuWrapperProps) {
  return <MobileMenuContent user={user} />;
}
