import { redirect } from 'next/navigation';
import { authServer } from '@/lib/utils/auth-server';
import { BoxContentsTable } from '@/components/admin';

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';

export default async function BoxContentsPage() {
  const { data: user } = await authServer.getCurrentUser();

  if (!user) {
    redirect('/login');
  }

  if (user.role !== 'admin') {
    redirect('/dashboard');
  }

  return <BoxContentsTable />;
}
