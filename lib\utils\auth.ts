import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';

export type AuthUser = {
  id: string;
  email: string;
  name?: string;
  role?: 'user' | 'admin';
};

export type SignUpData = {
  email: string;
  password: string;
  name: string;
  address?: string;
  phone?: string;
  role?: 'user' | 'admin';
};

export type SignInData = {
  email: string;
  password: string;
};

export type AuthError = {
  message: string;
  code?: string;
};

export type AuthResponse<T = unknown> = {
  data: T | null;
  error: AuthError | null;
};

// Client-side auth functions
export const authClient = {
  // Sign up new user
  async signUp(userData: SignUpData): Promise<AuthResponse<AuthUser>> {
    try {
      const supabase = createClient();

      const { data, error } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
          data: {
            name: userData.name,
            address: userData.address || 'Address to be provided',
            phone: userData.phone || '',
            role: userData.role || 'user',
          },
        },
      });

      if (error) {
        return {
          data: null,
          error: { message: error.message, code: error.message },
        };
      }

      if (!data.user) {
        return { data: null, error: { message: 'Failed to create user' } };
      }

      return {
        data: {
          id: data.user.id,
          email: data.user.email!,
          name: userData.name,
          role: userData.role || 'user',
        },
        error: null,
      };
    } catch (error) {
      console.error('Sign up error:', error);
      return {
        data: null,
        error: { message: 'An unexpected error occurred during sign up' },
      };
    }
  },

  // Sign in existing user
  async signIn(credentials: SignInData): Promise<AuthResponse<AuthUser>> {
    try {
      const supabase = createClient();

      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        return {
          data: null,
          error: { message: error.message, code: error.message },
        };
      }

      if (!data.user) {
        return { data: null, error: { message: 'Failed to sign in' } };
      }

      // Get user role from subscribers table
      const { data: subscriber } = await supabase
        .from('subscribers')
        .select('role, name')
        .eq('user_id', data.user.id)
        .single();

      return {
        data: {
          id: data.user.id,
          email: data.user.email!,
          name: subscriber?.name || data.user.user_metadata?.name,
          role: subscriber?.role || 'user',
        },
        error: null,
      };
    } catch (error) {
      console.error('Sign in error:', error);
      return {
        data: null,
        error: { message: 'An unexpected error occurred during sign in' },
      };
    }
  },

  // Sign out user
  async signOut(): Promise<AuthResponse<null>> {
    try {
      const supabase = createClient();
      const { error } = await supabase.auth.signOut();

      if (error) {
        return { data: null, error: { message: error.message } };
      }

      return { data: null, error: null };
    } catch (error) {
      console.error('Sign out error:', error);
      return {
        data: null,
        error: { message: 'An unexpected error occurred during sign out' },
      };
    }
  },

  // Get current user
  async getCurrentUser(): Promise<AuthResponse<AuthUser>> {
    try {
      const supabase = createClient();
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser();

      if (error) {
        return { data: null, error: { message: error.message } };
      }

      if (!user) {
        return { data: null, error: { message: 'No user found' } };
      }

      // Get user role from subscribers table
      const { data: subscriber } = await supabase
        .from('subscribers')
        .select('role, name')
        .eq('user_id', user.id)
        .single();

      return {
        data: {
          id: user.id,
          email: user.email!,
          name: subscriber?.name || user.user_metadata?.name,
          role: subscriber?.role || 'user',
        },
        error: null,
      };
    } catch (error) {
      console.error('Get current user error:', error);
      return {
        data: null,
        error: { message: 'An unexpected error occurred while getting user' },
      };
    }
  },

  // Reset password
  async resetPassword(email: string): Promise<AuthResponse<null>> {
    try {
      const supabase = createClient();
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/callback?next=/auth/reset-password`,
      });

      if (error) {
        return { data: null, error: { message: error.message } };
      }

      return { data: null, error: null };
    } catch (error) {
      console.error('Reset password error:', error);
      return {
        data: null,
        error: {
          message: 'An unexpected error occurred during password reset',
        },
      };
    }
  },

  // Update password
  async updatePassword(newPassword: string): Promise<AuthResponse<null>> {
    try {
      const supabase = createClient();
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        return { data: null, error: { message: error.message } };
      }

      return { data: null, error: null };
    } catch (error) {
      console.error('Update password error:', error);
      return {
        data: null,
        error: {
          message: 'An unexpected error occurred during password update',
        },
      };
    }
  },
};

// Store integration functions (for client-side use)
export const authStore = {
  // Sign in and update store
  async signInWithStore(credentials: SignInData) {
    const { data, error } = await authClient.signIn(credentials);

    if (data && !error) {
      // Get the actual Supabase user to store
      const supabase = createClient();
      const {
        data: { user: supabaseUser },
      } = await supabase.auth.getUser();

      if (supabaseUser) {
        // Import store dynamically to avoid SSR issues
        const { useUserStore } = await import('@/lib/store/users');
        const { updateUser } = useUserStore.getState();
        updateUser(supabaseUser);

        // Add user to audience for email marketing (non-blocking)
        try {
          if (data.email) {
            // Import the server action dynamically
            const { addUserToAudience } = await import(
              '@/lib/actions/audience'
            );
            // Don't await this to avoid blocking the sign-in flow
            addUserToAudience(data.email).catch((audienceError) => {
              console.error(
                'Failed to add user to audience during sign-in:',
                audienceError
              );
            });
          }
        } catch (audienceError) {
          // Don't fail the sign-in if audience addition fails
          console.error(
            'Error adding user to audience during sign-in:',
            audienceError
          );
        }
      }
    }

    return { data, error };
  },

  // Sign out and clear store
  async signOutWithStore() {
    const { error } = await authClient.signOut();

    if (!error) {
      // Import store dynamically to avoid SSR issues
      const { useUserStore } = await import('@/lib/store/users');
      const { removeUser } = useUserStore.getState();
      removeUser(null); // Pass null as expected by the store
    }

    return { error };
  },

  // Get current user from store
  getCurrentUserFromStore() {
    if (typeof window === 'undefined') {
      return null; // Server-side, return null
    }

    try {
      // Use dynamic import instead of require
      return new Promise<AuthUser | null>(async (resolve) => {
        try {
          const { useUserStore } = await import('@/lib/store/users');
          const { user } = useUserStore.getState();

          if (!user) {
            resolve(null);
            return;
          }

          // Convert Supabase User to AuthUser format
          resolve({
            id: user.id,
            email: user.email || '',
            name: user.user_metadata?.name,
            role: user.user_metadata?.role || 'user',
          } as AuthUser);
        } catch {
          resolve(null);
        }
      });
    } catch {
      return null;
    }
  },

  // Initialize store with current session
  async initializeStore() {
    if (typeof window === 'undefined') {
      return; // Server-side, do nothing
    }

    try {
      // Get the actual Supabase user
      const supabase = createClient();
      const {
        data: { user: supabaseUser },
      } = await supabase.auth.getUser();

      if (supabaseUser) {
        const { useUserStore } = await import('@/lib/store/users');
        const { updateUser } = useUserStore.getState();
        updateUser(supabaseUser);
      }
    } catch (error) {
      console.error('Failed to initialize store:', error);
    }
  },
};

// Utility functions
export const authUtils = {
  // Validate email format
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Validate password strength
  isValidPassword(password: string): { isValid: boolean; message?: string } {
    if (password.length < 8) {
      return {
        isValid: false,
        message: 'Password must be at least 8 characters long',
      };
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return {
        isValid: false,
        message: 'Password must contain at least one lowercase letter',
      };
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return {
        isValid: false,
        message: 'Password must contain at least one uppercase letter',
      };
    }
    if (!/(?=.*\d)/.test(password)) {
      return {
        isValid: false,
        message: 'Password must contain at least one number',
      };
    }
    return { isValid: true };
  },

  // Format auth error messages
  formatAuthError(error: string): string {
    switch (error) {
      case 'Invalid login credentials':
        return 'Invalid email or password. Please try again.';
      case 'Email not confirmed':
        return 'Please check your email and click the confirmation link.';
      case 'User already registered':
        return 'An account with this email already exists.';
      case 'Password should be at least 6 characters':
        return 'Password must be at least 6 characters long.';
      default:
        return error;
    }
  },

  // Generate redirect URL for auth flows
  getRedirectUrl(path: string = '/'): string {
    const baseUrl =
      typeof window !== 'undefined'
        ? window.location.origin
        : process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    return `${baseUrl}${path}`;
  },

  // Get role-based redirect path
  getRoleBasedRedirect(role?: string): string {
    return role === 'admin' ? '/admin' : '/dashboard';
  },

  // Perform role-based redirect
  redirectBasedOnRole(user: AuthUser, fallbackPath?: string): void {
    if (typeof window !== 'undefined') {
      const redirectPath = fallbackPath || this.getRoleBasedRedirect(user.role);
      window.location.href = redirectPath;
    }
  },
};

// Toast messages for different auth actions
const TOAST_MESSAGES = {
  signIn: {
    loading: 'Signing you in...',
    success: (name?: string) => `Welcome back${name ? `, ${name}` : ''}! 🎉`,
    error: 'Failed to sign in. Please check your credentials.',
  },
  signUp: {
    loading: 'Creating your account...',
    success:
      'Account created successfully! Please check your email to verify your account. 📧',
    error:
      'Failed to create account. Please check your information and try again.',
  },
  signOut: {
    loading: 'Signing you out...',
    success: 'You have been signed out successfully. See you soon! 👋',
    error: 'Failed to sign out. Please try again.',
  },
  forgotPassword: {
    loading: 'Sending reset link...',
    success: (email: string) =>
      `Password reset link sent to ${email}! Check your inbox. 📧`,
    error: 'Failed to send reset link. Please try again.',
  },
  resetPassword: {
    loading: 'Updating your password...',
    success:
      'Password updated successfully! You can now sign in with your new password. ✅',
    error: 'Failed to update password. Please try again.',
  },
} as const;

// Auth functions with toast notifications and redirect
export const authWithToast = {
  // Sign in with toast and role-based redirect
  async signIn(credentials: SignInData, redirectTo?: string) {
    return toast.promise(
      new Promise<AuthResponse<AuthUser>>(async (resolve, reject) => {
        try {
          const result = await authStore.signInWithStore(credentials);

          if (result.error) {
            reject(new Error(result.error.message));
          } else {
            // Auto-redirect on successful sign in with role-based routing
            if (typeof window !== 'undefined' && result.data) {
              // Determine redirect destination based on user role
              const finalRedirectTo =
                redirectTo ||
                (result.data.role === 'admin' ? '/admin' : '/dashboard');

              // Small delay to let the toast show
              setTimeout(() => {
                window.location.href = finalRedirectTo;
              }, 1000);
            }
            resolve(result);
          }
        } catch (error) {
          reject(error);
        }
      }),
      {
        loading: TOAST_MESSAGES.signIn.loading,
        success: (result: AuthResponse<AuthUser>) => {
          if (result.data) {
            return `${TOAST_MESSAGES.signIn.success(result.data.name)}`;
          }
          return TOAST_MESSAGES.signIn.success();
        },
        error: (error) => {
          console.error('Sign in error:', error);
          return TOAST_MESSAGES.signIn.error;
        },
      }
    );
  },

  // Sign up with toast
  async signUp(userData: SignUpData) {
    return toast.promise(
      new Promise<AuthResponse<AuthUser>>(async (resolve, reject) => {
        try {
          const result = await authClient.signUp(userData);

          if (result.error) {
            reject(new Error(result.error.message));
          } else {
            resolve(result);
          }
        } catch (error) {
          reject(error);
        }
      }),
      {
        loading: TOAST_MESSAGES.signUp.loading,
        success: () => TOAST_MESSAGES.signUp.success,
        error: (error) => {
          console.error('Sign up error:', error);
          return TOAST_MESSAGES.signUp.error;
        },
      }
    );
  },

  // Sign out with toast
  async signOut() {
    return toast.promise(
      new Promise<{ error: AuthError | null }>(async (resolve, reject) => {
        try {
          const result = await authStore.signOutWithStore();

          if (result.error) {
            reject(new Error(result.error.message));
          } else {
            resolve(result);
          }
        } catch (error) {
          reject(error);
        }
      }),
      {
        loading: TOAST_MESSAGES.signOut.loading,
        success: () => TOAST_MESSAGES.signOut.success,
        error: (error) => {
          console.error('Sign out error:', error);
          return TOAST_MESSAGES.signOut.error;
        },
      }
    );
  },

  // Forgot password with toast
  async forgotPassword(email: string) {
    return toast.promise(
      new Promise<AuthResponse<null>>(async (resolve, reject) => {
        try {
          const result = await authClient.resetPassword(email);

          if (result.error) {
            reject(new Error(result.error.message));
          } else {
            resolve(result);
          }
        } catch (error) {
          reject(error);
        }
      }),
      {
        loading: TOAST_MESSAGES.forgotPassword.loading,
        success: () => TOAST_MESSAGES.forgotPassword.success(email),
        error: (error) => {
          console.error('Forgot password error:', error);
          return TOAST_MESSAGES.forgotPassword.error;
        },
      }
    );
  },

  // Reset password with toast
  async resetPassword(newPassword: string) {
    return toast.promise(
      new Promise<AuthResponse<null>>(async (resolve, reject) => {
        try {
          const result = await authClient.updatePassword(newPassword);

          if (result.error) {
            reject(new Error(result.error.message));
          } else {
            resolve(result);
          }
        } catch (error) {
          reject(error);
        }
      }),
      {
        loading: TOAST_MESSAGES.resetPassword.loading,
        success: () => TOAST_MESSAGES.resetPassword.success,
        error: (error) => {
          console.error('Reset password error:', error);
          return TOAST_MESSAGES.resetPassword.error;
        },
      }
    );
  },
};

// Custom toast functions for specific scenarios
export const authToast = {
  // Success toasts
  success: {
    emailVerificationSent: (email: string) =>
      toast.success(
        `Verification email sent to ${email}! Please check your inbox. 📧`
      ),

    passwordResetSent: (email: string) =>
      toast.success(
        `Password reset link sent to ${email}! Check your inbox. 📧`
      ),

    accountCreated: () =>
      toast.success(
        'Account created successfully! Please verify your email. ✅'
      ),

    passwordUpdated: () => toast.success('Password updated successfully! ✅'),

    profileUpdated: () => toast.success('Profile updated successfully! ✅'),

    welcomeBack: (name?: string) =>
      toast.success(`Welcome back${name ? `, ${name}` : ''}! 🎉`),

    signedOut: () =>
      toast.success('You have been signed out successfully. See you soon! 👋'),
  },

  // Error toasts
  error: {
    invalidCredentials: () =>
      toast.error('Invalid email or password. Please try again.'),

    emailAlreadyExists: () =>
      toast.error('An account with this email already exists.'),

    weakPassword: () =>
      toast.error('Password is too weak. Please choose a stronger password.'),

    emailNotVerified: () =>
      toast.error('Please verify your email before signing in.'),

    invalidResetLink: () =>
      toast.error('Invalid or expired reset link. Please request a new one.'),

    networkError: () =>
      toast.error('Network error. Please check your connection and try again.'),

    unexpectedError: () =>
      toast.error('An unexpected error occurred. Please try again.'),

    sessionExpired: () =>
      toast.error('Your session has expired. Please sign in again.'),
  },

  // Info toasts
  info: {
    checkEmail: (email: string) =>
      toast.info(
        `Please check your email (${email}) for further instructions.`
      ),

    redirecting: (destination: string) =>
      toast.info(`Redirecting you to ${destination}...`),

    sessionRefreshed: () => toast.info('Session refreshed successfully.'),
  },
};

// Helper function to handle auth errors with appropriate toasts
export const handleAuthError = (error: any) => {
  const errorMessage = error?.message || error || 'Unknown error';
  const formattedError = authUtils.formatAuthError(errorMessage);

  // Map specific errors to custom toasts
  switch (errorMessage) {
    case 'Invalid login credentials':
      return authToast.error.invalidCredentials();
    case 'User already registered':
      return authToast.error.emailAlreadyExists();
    case 'Email not confirmed':
      return authToast.error.emailNotVerified();
    case 'Password should be at least 6 characters':
      return authToast.error.weakPassword();
    default:
      return toast.error(formattedError);
  }
};
