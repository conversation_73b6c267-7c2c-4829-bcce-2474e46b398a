# AsedaFoods CSA Subscription Model Analysis & Implementation Plan

## Farmhand CSA Model Analysis

### 🔍 **Key Features Observed from Spiritual Food CSA**

#### **1. Subscription Personalization**

- **Box Size Selection**: Fresh Produce Share (single size shown)
- **Frequency Options**: Weekly or Bi-weekly delivery
- **Payment Plans**: Multiple prepayment options with discounts
  - 4 boxes: $44.00/ea (standard price)
  - 13 boxes: $42.00/ea (5% discount)
  - 25 boxes: $40.00/ea (9% discount)

#### **2. User Experience Flow**

- **3-Step Process**: Personalize → Location → Checkout
- **Auto-renewal**: Plans automatically renew after completion
- **Clear Pricing**: Transparent pricing with savings highlighted
- **Immediate Feedback**: Real-time subtotal calculation

#### **3. Business Model Benefits**

- **Cash Flow**: Prepayment improves cash flow
- **Customer Retention**: Longer commitments with incentives
- **Predictable Revenue**: Subscription-based recurring income
- **Inventory Planning**: Advance orders help with planning

## AsedaFoods Implementation Plan

### 🎯 **Our Subscription Model**

#### **1. Box Size Options** (From current landing page)

```
Small Box - $25.99
- 8-10 seasonal items
- Serves 1-2 people
- Perfect for individuals or couples

Medium Box - $35.99
- 12-15 seasonal items
- Serves 3-4 people
- Great for small families

Large Box - $45.99
- 18-22 seasonal items
- Serves 5-6 people
- Ideal for large families
```

#### **2. Frequency Options** (From current landing page)

```
WEEKLY DELIVERY
BI-WEEKLY DELIVERY
MONTHLY DELIVERY (10% discount)
```

#### **3. Payment Plans** (Inspired by Farmhand)

```
- 4 deliveries: Standard price
- 12 deliveries: 5% discount
- 24 deliveries: 10% discount
```

### 🏗️ **Technical Implementation Plan**

#### **Phase 1: Core Subscription System**

**Database Schema Updates:**

```sql
-- Enhanced subscriptions table
ALTER TABLE subscriptions ADD COLUMN:
- box_size VARCHAR CHECK (box_size IN ('small', 'medium', 'large'))
- frequency VARCHAR CHECK (frequency IN ('weekly', 'biweekly', 'monthly'))
- payment_plan VARCHAR CHECK (payment_plan IN ('4_deliveries', '12_deliveries', '24_deliveries'))
- discount_percentage DECIMAL(5,2) DEFAULT 0
- deliveries_remaining INTEGER
- auto_renew BOOLEAN DEFAULT true
- next_delivery_date DATE
- status VARCHAR CHECK (status IN ('active', 'paused', 'cancelled', 'completed'))
```

**Frontend Components:**

1. **Subscription Wizard** (3-step process like Farmhand)

   - Step 1: Box size and frequency selection
   - Step 2: Payment plan selection
   - Step 3: Delivery preferences and checkout

2. **Pricing Calculator**
   - Dynamic pricing based on selections
   - Savings visualization

#### **Phase 2: User Dashboard**

**Member Dashboard:**

```
/dashboard/
├── overview/ (subscription status, next delivery)
├── subscription/ (manage plan)
├── deliveries/ (history, upcoming)
└── billing/ (payment methods, invoices)
```

**Admin Dashboard:**

```
/admin/
├── subscriptions/ (all active subscriptions)
├── deliveries/ (delivery planning)
├── inventory/ (box contents)
└── customers/ (customer management)
```

### 🎨 **UI/UX Implementation**

#### **Subscription Flow Design**

**Step 1: Box Size & Frequency**

```tsx
// Box size selection with visual cards
<BoxSizeSelector>
  {subscriptionPlans.map(plan => (
    <BoxCard
      name={plan.name}
      price={plan.price}
      description={plan.description}
      features={plan.features}
      isPopular={plan.isPopular}
      selected={selectedBox === plan.id}
      onClick={() => setSelectedBox(plan.id)}
    />
  ))}
</BoxSizeSelector>

// Frequency toggle
<FrequencySelector>
  {pickupFrequencies.map(frequency => (
    <FrequencyOption
      name={frequency.name}
      description={frequency.description}
      discount={frequency.discount}
      selected={selectedFrequency === frequency.id}
      onClick={() => setSelectedFrequency(frequency.id)}
    />
  ))}
</FrequencySelector>
```

**Step 2: Payment Plan**

```tsx
<PaymentPlanSelector>
  {paymentPlans.map(plan => (
    <PlanCard
      deliveries={plan.deliveries}
      pricePerBox={plan.pricePerBox}
      totalPrice={plan.totalPrice}
      discount={plan.discount}
      savings={plan.savings}
    />
  ))}
</PaymentPlanSelector>

<PricingBreakdown>
  <Line>Subtotal: ${subtotal}</Line>
  <Line>Discount: -${discount}</Line>
  <Line>Total: ${total}</Line>
</PricingBreakdown>
```

**Step 3: Checkout**

```tsx
<DeliveryPreferences>
  <AddressSelector />
  <SpecialInstructions />
</DeliveryPreferences>

<PaymentSection>
  <StripeCheckout />
  <OrderSummary />
</PaymentSection>
```

### 💰 **Pricing Implementation**

#### **Discount Structure**

```typescript
const DISCOUNT_TIERS = {
  weekly: {
    '4_deliveries': { discount: 0 },
    '12_deliveries': { discount: 5 },
    '24_deliveries': { discount: 10 },
  },
  biweekly: {
    '4_deliveries': { discount: 0 },
    '12_deliveries': { discount: 5 },
    '24_deliveries': { discount: 10 },
  },
  monthly: {
    '4_deliveries': { discount: 10 },
    '12_deliveries': { discount: 15 },
    '24_deliveries': { discount: 20 },
  },
};

const calculatePrice = (basePrice, frequency, plan) => {
  const discount = DISCOUNT_TIERS[frequency][plan].discount;
  return basePrice * (1 - discount / 100);
};
```

### 🚀 **Implementation Timeline**

#### **Week 1: Core Subscription System**

- Database schema updates
- 3-step subscription wizard
- Basic Stripe integration

#### **Week 2: Dashboard & Management**

- Customer dashboard
- Admin dashboard
- Basic analytics

#### **Week 3: Testing & Launch**

- End-to-end testing
- Payment flow testing
- Production deployment

## 🔗 **Integration with Current Project**

### **Updated Database Schema**

```sql
-- Update existing subscriptions table
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS:
  box_size VARCHAR CHECK (box_size IN ('small', 'medium', 'large')),
  frequency VARCHAR CHECK (frequency IN ('weekly', 'biweekly', 'monthly')),
  payment_plan VARCHAR CHECK (payment_plan IN ('4_deliveries', '12_deliveries', '24_deliveries')),
  discount_percentage DECIMAL(5,2) DEFAULT 0,
  deliveries_remaining INTEGER,
  auto_renew BOOLEAN DEFAULT true,
  next_delivery_date DATE;
```

### **Implementation in /get-a-box Page**

```typescript
// /app/get-a-box/page.tsx
'use client';

import { useState } from 'react';
import { useUser } from '@/lib/hooks/useUser';
import { redirect } from 'next/navigation';
import StepBoxSelection from './components/StepBoxSelection';
import StepPaymentPlan from './components/StepPaymentPlan';
import StepCheckout from './components/StepCheckout';

export default function GetABoxPage() {
  const { isAuthenticated } = useUser();
  const [currentStep, setCurrentStep] = useState(1);
  const [subscriptionData, setSubscriptionData] = useState({
    boxSize: '',
    frequency: '',
    paymentPlan: '',
    deliveryPreferences: {},
  });

  if (!isAuthenticated) {
    redirect('/auth/signin?redirectTo=/get-a-box');
  }

  const steps = [
    { number: 1, title: 'Select Box', component: StepBoxSelection },
    { number: 2, title: 'Payment Plan', component: StepPaymentPlan },
    { number: 3, title: 'Checkout', component: StepCheckout },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <SubscriptionWizard
        steps={steps}
        currentStep={currentStep}
        subscriptionData={subscriptionData}
        onStepChange={setCurrentStep}
        onDataChange={setSubscriptionData}
      />
    </div>
  );
}
```
