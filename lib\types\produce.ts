import { Database } from '@/lib/supabase/types';

// Database types
export type ProduceItem = Database['public']['Tables']['produce_items']['Row'];
export type ProduceItemInsert =
  Database['public']['Tables']['produce_items']['Insert'];
export type ProduceItemUpdate =
  Database['public']['Tables']['produce_items']['Update'];

export type UserProducePreference =
  Database['public']['Tables']['user_produce_preferences']['Row'];
export type UserProducePreferenceInsert =
  Database['public']['Tables']['user_produce_preferences']['Insert'];
export type UserProducePreferenceUpdate =
  Database['public']['Tables']['user_produce_preferences']['Update'];

// Enums
export type ProduceCategory = 'vegetables' | 'fruits' | 'herbs' | 'greens';
export type PreferenceType = 'never_send' | 'preferred';

// Extended types with relationships
export interface ProduceItemWithPreference extends ProduceItem {
  user_preference?: UserProducePreference;
  is_never_send?: boolean;
  is_preferred?: boolean;
}

export interface UserProducePreferenceWithItem extends UserProducePreference {
  produce_item?: ProduceItem;
}

// Form types
export interface ProducePreferencesFormData {
  never_send_items: string[]; // Array of produce item IDs
  preferred_items?: string[]; // Array of produce item IDs (future feature)
}

// Constants
export const PRODUCE_CATEGORIES: Record<ProduceCategory, string> = {
  vegetables: 'Vegetables',
  fruits: 'Fruits',
  herbs: 'Herbs',
  greens: 'Greens',
} as const;

export const PREFERENCE_TYPES: Record<PreferenceType, string> = {
  never_send: 'Never Send',
  preferred: 'Preferred',
} as const;

// Validation constants
export const MAX_NEVER_SEND_ITEMS = 3;
export const MAX_PREFERRED_ITEMS = 5;
