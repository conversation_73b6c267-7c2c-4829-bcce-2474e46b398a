import { Resend } from 'resend';
import { NewsletterWelcome } from '@/lib/emails/templates/newsletter/welcome';

// Initialize Resend with API key from environment variables
const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY as string);

export interface EmailOptions {
  to: string | string[];
  subject: string;
  react: React.ReactNode;
  from?: string;
}

export class EmailService {
  private static readonly DEFAULT_FROM =
    'Farmers AsedaFoods <<EMAIL>>';
  private static readonly GENERAL_AUDIENCE_ID =
    process.env.NEXT_PUBLIC_RESEND_GENERAL_AUDIENCE_ID!;

  /**
   * Send an email using Resend
   */
  static async sendEmail({
    to,
    subject,
    react,
    from = EmailService.DEFAULT_FROM,
  }: EmailOptions) {
    try {
      const { data, error } = await resend.emails.send({
        from,
        to,
        subject,
        react,
      });

      if (error) {
        console.error('Failed to send email', { error, to, subject });
        return { success: false, error };
      }

      console.log('Email sent successfully', { id: data?.id, to, subject });
      return { success: true, data };
    } catch (error) {
      console.error('Exception when sending email', { error, to, subject });
      return { success: false, error };
    }
  }

  /**
   * Add email to Resend audience
   */
  static async addToAudience(
    email: string,
    audienceId: string = EmailService.GENERAL_AUDIENCE_ID
  ) {
    try {
      console.log(`Adding ${email} to audience: ${audienceId}`);

      const { data, error } = await resend.contacts.create({
        email,
        audienceId,
        unsubscribed: false,
      });

      if (error) {
        console.error('Failed to add contact to audience', {
          error,
          email,
          audienceId,
        });
        return { success: false, error };
      }

      console.log('Contact added to audience successfully', {
        contactId: data?.id,
        email,
        audienceId,
      });
      return {
        success: true,
        data: { contactId: data?.id, email, audienceId },
      };
    } catch (error) {
      console.error('Exception when adding to audience', {
        error,
        email,
        audienceId,
      });
      return { success: false, error };
    }
  }

  /**
   * Remove email from Resend audience
   */
  static async removeFromAudience(
    email: string,
    audienceId: string = EmailService.GENERAL_AUDIENCE_ID
  ) {
    try {
      console.log(`Removing ${email} from audience: ${audienceId}`);

      const { data, error } = await resend.contacts.remove({
        email,
        audienceId,
      });

      if (error) {
        console.error('Failed to remove contact from audience', {
          error,
          email,
          audienceId,
        });
        return { success: false, error };
      }

      console.log('Contact removed from audience successfully', {
        email,
        audienceId,
        deleted: data?.deleted,
      });
      return {
        success: true,
        data: { email, audienceId, deleted: data?.deleted },
      };
    } catch (error) {
      console.error('Exception when removing from audience', {
        error,
        email,
        audienceId,
      });
      return { success: false, error };
    }
  }

  /**
   * Send newsletter welcome email
   */
  static async sendNewsletterWelcome(to: string) {
    return EmailService.sendEmail({
      to,
      subject: 'Welcome to AsedaFoods Newsletter!',
      react: NewsletterWelcome({ email: to }),
    });
  }

  /**
   * Send welcome email for new user accounts
   */
  static async sendWelcomeEmail(to: string, name: string) {
    const { WelcomeEmail } = await import(
      '@/lib/emails/templates/auth/welcome'
    );
    return EmailService.sendEmail({
      to,
      subject: 'Welcome to AsedaFoods CSA! Your Account is Ready',
      react: WelcomeEmail({ name }),
    });
  }

  /**
   * Send subscription confirmation email
   */
  static async sendSubscriptionConfirmation(
    to: string,
    subscriberName: string,
    subscriptionData: {
      boxSize: string;
      frequency: string;
      paymentPlan: string;
      pickupLocation: string;
      nextDeliveryDate: Date;
      totalPrice: number;
      deliveriesRemaining?: number;
      specialInstructions?: string;
    }
  ) {
    const { SubscriptionConfirmation } = await import(
      '@/lib/emails/templates/subscription/confirmation'
    );
    return EmailService.sendEmail({
      to,
      subject: 'Your AsedaFoods CSA Subscription is Confirmed!',
      react: SubscriptionConfirmation({ subscriberName, subscriptionData }),
    });
  }

  /**
   * Send payment confirmation email
   */
  static async sendPaymentConfirmation(
    to: string,
    subscriberName: string,
    paymentData: {
      amount: number;
      paymentMethod: string;
      transactionId: string;
      paymentDate: Date;
    },
    subscriptionData: {
      boxSize: string;
      frequency: string;
      nextDeliveryDate: Date;
      deliveriesRemaining?: number;
    }
  ) {
    const { PaymentConfirmation } = await import(
      '@/lib/emails/templates/subscription/payment'
    );
    return EmailService.sendEmail({
      to,
      subject: 'Payment Received - Thank You!',
      react: PaymentConfirmation({
        subscriberName,
        paymentData,
        subscriptionData,
      }),
    });
  }

  /**
   * Send subscription cancellation email
   */
  static async sendSubscriptionCancellation(
    to: string,
    subscriberName: string,
    subscriptionData: {
      boxSize: string;
      frequency: string;
      cancellationDate: Date;
      lastDeliveryDate?: Date;
      refundAmount?: number;
    }
  ) {
    const { SubscriptionCancellation } = await import(
      '@/lib/emails/templates/subscription/cancellation'
    );
    return EmailService.sendEmail({
      to,
      subject: 'Your Subscription Has Been Cancelled',
      react: SubscriptionCancellation({ subscriberName, subscriptionData }),
    });
  }

  /**
   * Send subscription pause email
   */
  static async sendSubscriptionPause(
    to: string,
    subscriberName: string,
    subscriptionData: {
      boxSize: string;
      frequency: string;
      pauseStartDate: Date;
      pauseEndDate: Date;
      resumeDate: Date;
    }
  ) {
    const { SubscriptionPause } = await import(
      '@/lib/emails/templates/subscription/pause'
    );
    return EmailService.sendEmail({
      to,
      subject: 'Your Subscription Has Been Paused',
      react: SubscriptionPause({ subscriberName, subscriptionData }),
    });
  }

  /**
   * Send delivery reminder email
   */
  static async sendDeliveryReminder(
    to: string,
    subscriberName: string,
    deliveryData: {
      deliveryDate: Date;
      pickupLocation: string;
      pickupHours: string;
      boxSize: string;
      boxContents?: string[];
      specialInstructions?: string;
    }
  ) {
    const { DeliveryReminder } = await import(
      '@/lib/emails/templates/subscription/delivery-reminder'
    );
    return EmailService.sendEmail({
      to,
      subject: 'Your Fresh Box is Ready for Pickup Soon!',
      react: DeliveryReminder({ subscriberName, deliveryData }),
    });
  }

  /**
   * Send delivery processed email
   */
  static async sendDeliveryProcessed(
    to: string,
    subscriberName: string,
    deliveryData: {
      deliveryDate: Date;
      pickupLocation: string;
      boxSize: string;
      boxContents?: string[];
      nextDeliveryDate?: Date;
      deliveriesRemaining?: number;
    }
  ) {
    const { DeliveryProcessed } = await import(
      '@/lib/emails/templates/subscription/delivery-processed'
    );
    return EmailService.sendEmail({
      to,
      subject: 'Your Fresh Box Has Been Processed',
      react: DeliveryProcessed({ subscriberName, deliveryData }),
    });
  }

  /**
   * Send delivery ready for pickup email
   */
  static async sendDeliveryReady(
    to: string,
    subscriberName: string,
    deliveryData: {
      deliveryDate: Date;
      pickupLocation: string;
      pickupHours: string;
      boxSize: string;
      boxContents?: string[];
      specialInstructions?: string;
      deliveryId: string;
    }
  ) {
    const { DeliveryReady } = await import(
      '@/lib/emails/templates/delivery/delivery-ready'
    );
    return EmailService.sendEmail({
      to,
      subject: 'Your Fresh Box is Ready for Pickup! 📦',
      react: DeliveryReady({ subscriberName, deliveryData }),
    });
  }

  /**
   * Send delivery confirmed email
   */
  static async sendDeliveryConfirmed(
    to: string,
    subscriberName: string,
    deliveryData: {
      deliveryDate: Date;
      pickupLocation: string;
      boxSize: string;
      boxContents?: string[];
      nextDeliveryDate?: Date;
      deliveriesRemaining?: number;
      deliveryId: string;
    }
  ) {
    const { DeliveryConfirmed } = await import(
      '@/lib/emails/templates/delivery/delivery-confirmed'
    );
    return EmailService.sendEmail({
      to,
      subject: 'Delivery Confirmed - Thank You! ✅',
      react: DeliveryConfirmed({ subscriberName, deliveryData }),
    });
  }

  /**
   * Send delivery cancelled email
   */
  static async sendDeliveryCancelled(
    to: string,
    subscriberName: string,
    deliveryData: {
      deliveryDate: Date;
      pickupLocation: string;
      boxSize: string;
      cancellationReason?: string;
      nextDeliveryDate?: Date;
      deliveryId: string;
    }
  ) {
    const { DeliveryCancelled } = await import(
      '@/lib/emails/templates/delivery/delivery-cancelled'
    );
    return EmailService.sendEmail({
      to,
      subject: 'Delivery Cancelled - Important Update',
      react: DeliveryCancelled({ subscriberName, deliveryData }),
    });
  }

  /**
   * Send delivery rescheduled email
   */
  static async sendDeliveryRescheduled(
    to: string,
    subscriberName: string,
    deliveryData: {
      originalDate: Date;
      newDate: Date;
      pickupLocation: string;
      boxSize: string;
      rescheduleReason?: string;
      deliveryId: string;
    }
  ) {
    const { DeliveryRescheduled } = await import(
      '@/lib/emails/templates/delivery/delivery-rescheduled'
    );
    return EmailService.sendEmail({
      to,
      subject: 'Delivery Rescheduled - New Pickup Date',
      react: DeliveryRescheduled({ subscriberName, deliveryData }),
    });
  }

  /**
   * Send renewal reminder email
   */
  static async sendRenewalReminder(
    to: string,
    subscriberName: string,
    subscriptionData: {
      boxSize: string;
      frequency: string;
      renewalDate: Date;
      renewalPrice: number;
      deliveriesRemaining: number;
      autoRenew: boolean;
    }
  ) {
    const { RenewalReminder } = await import(
      '@/lib/emails/templates/subscription/renewal-reminder'
    );
    return EmailService.sendEmail({
      to,
      subject: 'Time to Renew Your AsedaFoods Subscription',
      react: RenewalReminder({ subscriberName, subscriptionData }),
    });
  }

  /**
   * Add email to general audience (convenience method)
   */
  static async addToGeneralAudience(email: string) {
    return EmailService.addToAudience(email, EmailService.GENERAL_AUDIENCE_ID);
  }

  /**
   * Remove email from general audience (convenience method)
   */
  static async removeFromGeneralAudience(email: string) {
    return EmailService.removeFromAudience(
      email,
      EmailService.GENERAL_AUDIENCE_ID
    );
  }

  /**
   * Send admin notification for new subscription
   */
  static async sendNewSubscriptionAlert(
    to: string,
    subscriberData: {
      id: string;
      name: string;
      email: string;
      phone?: string;
      address?: string;
    },
    subscriptionData: {
      id: string;
      boxSize: string;
      frequency: string;
      paymentPlan: string;
      pickupLocation: string;
      nextDeliveryDate: Date;
      totalPrice: number;
      deliveriesRemaining: number;
      specialInstructions?: string;
      createdAt: Date;
    }
  ) {
    const { NewSubscriptionAlert } = await import(
      '@/lib/emails/templates/admin/new-subscription'
    );
    return EmailService.sendEmail({
      to,
      subject: 'New CSA Subscription Received',
      react: NewSubscriptionAlert({ subscriberData, subscriptionData }),
    });
  }

  /**
   * Send admin notification for contact form submission
   */
  static async sendContactFormNotification(
    to: string,
    contactData: {
      id: string;
      name: string;
      email: string;
      message: string;
      submittedAt: Date;
    }
  ) {
    const { ContactFormNotification } = await import(
      '@/lib/emails/templates/admin/contact-form'
    );
    return EmailService.sendEmail({
      to,
      subject: 'New Contact Form Submission',
      react: ContactFormNotification({ contactData }),
    });
  }

  /**
   * Send admin notification for newsletter signup
   */
  static async sendNewsletterSignupNotification(
    to: string,
    newsletterData: {
      id: string;
      email: string;
      signupDate: Date;
      source?: string;
      isReactivation?: boolean;
    }
  ) {
    const { NewsletterSignupNotification } = await import(
      '@/lib/emails/templates/admin/newsletter-signup'
    );
    return EmailService.sendEmail({
      to,
      subject: newsletterData.isReactivation
        ? 'Newsletter Subscription Reactivated'
        : 'New Newsletter Subscription',
      react: NewsletterSignupNotification({ newsletterData }),
    });
  }

  /**
   * Get the general audience ID
   */
  static getGeneralAudienceId(): string {
    return EmailService.GENERAL_AUDIENCE_ID;
  }
}
