import { Text, Section, Row, Column } from '@react-email/components';
import { EmailLayout } from '../../components/EmailLayout';
import { EmailButton } from '../../components/EmailButton';
import { formatPrice, formatDate } from '@/lib/utils/format';

interface SubscriptionConfirmationProps {
  subscriberName: string;
  subscriptionData: {
    boxSize: string;
    frequency: string;
    paymentPlan: string;
    pickupLocation: string;
    nextDeliveryDate: Date;
    totalPrice: number;
    deliveriesRemaining?: number;
    specialInstructions?: string;
  };
}

export function SubscriptionConfirmation({
  subscriberName,
  subscriptionData,
}: SubscriptionConfirmationProps) {
  const {
    boxSize,
    frequency,
    paymentPlan,
    pickupLocation,
    nextDeliveryDate,
    totalPrice,
    deliveriesRemaining,
    specialInstructions,
  } = subscriptionData;

  return (
    <EmailLayout preview='Your AsedaFoods CSA subscription has been confirmed!'>
      <Text
        style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#1f2937',
          margin: '0 0 20px 0',
        }}
      >
        Thank you for your subscription, {subscriberName}!
      </Text>

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '0 0 20px 0',
        }}
      >
        We&apos;re excited to have you join our community of local food
        supporters. Your first box will be ready for pickup soon.
      </Text>

      <Section
        style={{
          backgroundColor: '#f7f9fa',
          padding: '20px',
          borderRadius: '8px',
          margin: '20px 0',
        }}
      >
        <Text
          style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#1f2937',
            margin: '0 0 15px 0',
          }}
        >
          Subscription Details
        </Text>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Box Size:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {boxSize}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Frequency:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {frequency}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Payment Plan:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {paymentPlan}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Pickup Location:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {pickupLocation}
            </Text>
          </Column>
        </Row>

        <Row style={{ marginBottom: '10px' }}>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Next Delivery:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {formatDate(nextDeliveryDate)}
            </Text>
          </Column>
        </Row>

        {deliveriesRemaining && (
          <Row style={{ marginBottom: '10px' }}>
            <Column style={{ width: '50%' }}>
              <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
                Deliveries Remaining:
              </Text>
            </Column>
            <Column style={{ width: '50%' }}>
              <Text
                style={{
                  fontSize: '14px',
                  fontWeight: 'bold',
                  color: '#1f2937',
                  margin: '0',
                }}
              >
                {deliveriesRemaining}
              </Text>
            </Column>
          </Row>
        )}

        <Row>
          <Column style={{ width: '50%' }}>
            <Text style={{ fontSize: '14px', color: '#6b7280', margin: '0' }}>
              Total Paid:
            </Text>
          </Column>
          <Column style={{ width: '50%' }}>
            <Text
              style={{
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#1f2937',
                margin: '0',
              }}
            >
              {formatPrice(totalPrice)}
            </Text>
          </Column>
        </Row>
      </Section>

      {specialInstructions && (
        <Section
          style={{
            backgroundColor: '#fef3c7',
            padding: '15px',
            borderRadius: '8px',
            margin: '20px 0',
          }}
        >
          <Text
            style={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: '#92400e',
              margin: '0 0 5px 0',
            }}
          >
            Special Instructions:
          </Text>
          <Text
            style={{
              fontSize: '14px',
              color: '#92400e',
              margin: '0',
            }}
          >
            {specialInstructions}
          </Text>
        </Section>
      )}

      <Text
        style={{
          fontSize: '16px',
          color: '#4b5563',
          lineHeight: '24px',
          margin: '20px 0',
        }}
      >
        You can view and manage your subscription anytime from your dashboard.
      </Text>

      <EmailButton
        href='https://asedafoods.org/dashboard'
        text='View Your Dashboard'
      />

      <Text
        style={{
          fontSize: '14px',
          color: '#6b7280',
          lineHeight: '20px',
          margin: '20px 0 0 0',
        }}
      >
        If you have any questions about your subscription, please don&apos;t
        hesitate to contact us. We&apos;re here to make your CSA experience
        amazing!
      </Text>
    </EmailLayout>
  );
}
