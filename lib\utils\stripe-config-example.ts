/**
 * Stripe Configuration Usage Examples
 * 
 * This file demonstrates how to use the stripe-config utility
 * in different scenarios throughout your application.
 */

import stripeConfigUtils from './stripe-config';

// Example 1: Basic usage in a server-side API route
export function exampleServerUsage() {
  try {
    // Get server configuration
    const config = stripeConfigUtils.config.getServerConfig();
    
    console.log('Using Stripe in', config.environment, 'mode');
    console.log('Key type:', config.keyType);
    
    // Use the secret key for Stripe initialization
    // const stripe = new Stripe(config.secretKey, { ... });
    
    return config;
  } catch (error) {
    console.error('Stripe configuration error:', error);
    throw error;
  }
}

// Example 2: Basic usage in a client-side component
export function exampleClientUsage() {
  try {
    // Get client configuration
    const config = stripeConfigUtils.config.getClientConfig();
    
    console.log('Client Stripe config:', {
      environment: config.environment,
      keyType: config.keyType,
      currency: config.currency
    });
    
    // Use the public key for Stripe initialization
    // const stripePromise = loadStripe(config.publicKey);
    
    return config;
  } catch (error) {
    console.error('Stripe client configuration error:', error);
    throw error;
  }
}

// Example 3: Environment validation
export function exampleValidation() {
  const validation = stripeConfigUtils.validation.validateEnvironmentVariables();
  
  if (!validation.isValid) {
    console.error('Stripe configuration errors:', validation.errors);
    throw new Error(`Missing Stripe environment variables: ${validation.errors.join(', ')}`);
  }
  
  console.log('✅ All required Stripe environment variables are set');
  return true;
}

// Example 4: Debug information
export function exampleDebugInfo() {
  const debugInfo = stripeConfigUtils.config.getDebugInfo();
  
  console.log('Stripe Configuration Debug Info:', {
    environment: debugInfo.environment,
    keyType: debugInfo.keyType,
    isDevelopment: debugInfo.isDevelopment,
    availableKeys: {
      testSecret: debugInfo.hasTestSecretKey,
      liveSecret: debugInfo.hasLiveSecretKey,
      testPublic: debugInfo.hasTestPublicKey,
      livePublic: debugInfo.hasLivePublicKey,
    }
  });
  
  return debugInfo;
}

// Example 5: Environment-specific logic
export function exampleEnvironmentLogic() {
  const { environment } = stripeConfigUtils;
  
  if (environment.isDevelopment()) {
    console.log('🧪 Running in development mode - using Stripe test keys');
    // Development-specific logic
    return {
      webhookEndpoint: 'http://localhost:3000/api/webhooks/stripe',
      logLevel: 'debug'
    };
  } else {
    console.log('🚀 Running in production mode - using Stripe live keys');
    // Production-specific logic
    return {
      webhookEndpoint: 'https://yourdomain.com/api/webhooks/stripe',
      logLevel: 'error'
    };
  }
}

// Example 6: Key validation
export function exampleKeyValidation() {
  try {
    const secretKey = stripeConfigUtils.keys.getSecretKey();
    const publicKey = stripeConfigUtils.keys.getPublicKey();
    
    const isValidSecret = stripeConfigUtils.validation.validateKeyFormat(secretKey, 'secret');
    const isValidPublic = stripeConfigUtils.validation.validateKeyFormat(publicKey, 'public');
    
    if (!isValidSecret) {
      throw new Error('Invalid Stripe secret key format');
    }
    
    if (!isValidPublic) {
      throw new Error('Invalid Stripe public key format');
    }
    
    console.log('✅ Stripe key formats are valid');
    return { secretKey, publicKey };
  } catch (error) {
    console.error('Key validation failed:', error);
    throw error;
  }
}

// Example 7: Complete setup function
export function setupStripeConfiguration() {
  console.log('🔧 Setting up Stripe configuration...');
  
  // 1. Validate environment variables
  exampleValidation();
  
  // 2. Get debug info
  const debugInfo = exampleDebugInfo();
  
  // 3. Validate key formats
  const keys = exampleKeyValidation();
  
  // 4. Get appropriate configuration
  const serverConfig = stripeConfigUtils.config.getServerConfig();
  const clientConfig = stripeConfigUtils.config.getClientConfig();
  
  console.log('✅ Stripe configuration setup complete');
  
  return {
    debugInfo,
    keys,
    serverConfig,
    clientConfig
  };
}
