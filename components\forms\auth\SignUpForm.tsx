'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { PasswordInput } from '@/components/ui/password-input';
import { authWithToast } from '@/lib/utils/auth';
import {
  signUpSchema,
  type SignUpFormData,
  validatePasswordStrength,
} from '@/lib/validations/auth-schemas';

interface SignUpFormProps {
  onSuccess?: (user: any) => void;
  redirectTo?: string;
  className?: string;
}

export default function SignUpForm({ onSuccess, className }: SignUpFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [userEmail, setUserEmail] = useState<string>('');

  const form = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      address: '',
      phone: '',
    },
  });

  const watchPassword = form.watch('password');
  const passwordStrength = watchPassword
    ? validatePasswordStrength(watchPassword)
    : null;

  const onSubmit = async (data: SignUpFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      // The toast will handle the promise and show appropriate messages
      await authWithToast.signUp({
        email: data.email,
        password: data.password,
        name: data.name,
        address: data.address,
        phone: data.phone,
      });

      // If we reach here, signup was successful
      // Set success state to show email verification message
      setIsSuccess(true);
      setUserEmail(data.email);

      // Call success callback if provided
      if (onSuccess) {
        onSuccess({ email: data.email, name: data.name });
        return;
      }

      // Don't redirect immediately - user needs to verify email first
    } catch (err) {
      console.error('Sign up error:', err);
      // The authWithToast.signUp already shows the error toast via toast.promise()
      // No need to set additional error state since the toast handles user feedback
    } finally {
      setIsLoading(false);
    }
  };

  // Show success message if signup was successful
  if (isSuccess) {
    return (
      <div className={className}>
        <div className='text-center space-y-4'>
          <div className='bg-green-50 border border-green-200 text-green-800 px-4 py-6 rounded-md'>
            <div className='flex items-center justify-center mb-3'>
              <svg
                className='w-8 h-8 text-green-600'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M3 8l7.89 7.89a2 2 0 002.83 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z'
                />
              </svg>
            </div>
            <h3 className='text-lg font-semibold mb-2'>Check Your Email!</h3>
            <p className='text-sm mb-3'>
              We&apos;ve sent a verification link to{' '}
              <strong>{userEmail}</strong>
            </p>
            <p className='text-sm text-green-700'>
              Please check your email and click the verification link to
              complete your account setup.
            </p>
          </div>
          <div className='text-sm text-gray-600'>
            <p>
              Didn&apos;t receive the email? Check your spam folder or contact
              support.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
          {error && (
            <div className='bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm'>
              {error}
            </div>
          )}

          <FormField
            control={form.control}
            name='name'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name</FormLabel>
                <FormControl>
                  <Input
                    type='text'
                    placeholder='Enter your full name'
                    autoComplete='name'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email address</FormLabel>
                <FormControl>
                  <Input
                    type='email'
                    placeholder='Enter your email'
                    autoComplete='email'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='password'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <PasswordInput
                    placeholder='Create a password'
                    autoComplete='new-password'
                    id='signup-password'
                    toggleLabel='Show password'
                    {...field}
                  />
                </FormControl>
                {passwordStrength && (
                  <div className='mt-2 space-y-1'>
                    <div className='flex space-x-1'>
                      {Array.from({ length: 4 }).map((_, i) => (
                        <div
                          key={i}
                          className={`h-1 flex-1 rounded ${
                            i < passwordStrength.score
                              ? passwordStrength.score <= 2
                                ? 'bg-red-500'
                                : passwordStrength.score === 3
                                  ? 'bg-yellow-500'
                                  : 'bg-green-500'
                              : 'bg-gray-200'
                          }`}
                        />
                      ))}
                    </div>
                    <div className='text-xs space-y-1'>
                      {passwordStrength.requirements.map((req, i) => (
                        <div
                          key={i}
                          className={`flex items-center space-x-1 ${
                            req.satisfied ? 'text-green-600' : 'text-gray-500'
                          }`}
                        >
                          <span>{req.satisfied ? '✓' : '○'}</span>
                          <span>{req.message}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='confirmPassword'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Confirm Password</FormLabel>
                <FormControl>
                  <PasswordInput
                    placeholder='Confirm your password'
                    autoComplete='new-password'
                    id='signup-confirm-password'
                    toggleLabel='Show password'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='address'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Address</FormLabel>
                <FormControl>
                  <Input
                    type='text'
                    placeholder='Enter your delivery address'
                    autoComplete='address-line1'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='phone'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number (Optional)</FormLabel>
                <FormControl>
                  <Input
                    type='tel'
                    placeholder='Enter your phone number'
                    autoComplete='tel'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type='submit' disabled={isLoading} className='w-full'>
            {isLoading ? 'Creating account...' : 'Create account'}
          </Button>
        </form>
      </Form>
    </div>
  );
}
