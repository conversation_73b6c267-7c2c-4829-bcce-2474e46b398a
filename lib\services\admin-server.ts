import { createClient } from '@/lib/supabase/server';
import { AdminStats, AdminResponse, PaginationParams } from '@/lib/types/admin';

export class AdminServerService {
  protected async getSupabase() {
    return await createClient();
  }

  /**
   * Get admin dashboard statistics
   */
  async getAdminStats(): Promise<AdminResponse<AdminStats>> {
    try {
      const supabase = await this.getSupabase();

      // Get total subscribers
      const { count: totalSubscribers } = await supabase
        .from('subscribers')
        .select('*', { count: 'exact', head: true });

      // Get active subscriptions
      const { count: activeSubscriptions } = await supabase
        .from('subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'active');

      // Get pending contact forms (assuming we'll add a status field)
      const { count: pendingContactForms } = await supabase
        .from('contact_submissions')
        .select('*', { count: 'exact', head: true });

      // Get total revenue from payments
      const { data: revenueData } = await supabase
        .from('payments')
        .select('amount')
        .eq('status', 'completed');

      const totalRevenue =
        revenueData?.reduce(
          (sum, payment) => sum + Number(payment.amount),
          0
        ) || 0;

      // Get monthly revenue (current month)
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const { data: monthlyRevenueData } = await supabase
        .from('payments')
        .select('amount')
        .eq('status', 'completed')
        .gte('created_at', startOfMonth.toISOString());

      const monthlyRevenue =
        monthlyRevenueData?.reduce(
          (sum, payment) => sum + Number(payment.amount),
          0
        ) || 0;

      // Get new subscribers this month
      const { count: newSubscribersThisMonth } = await supabase
        .from('subscribers')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', startOfMonth.toISOString());

      const stats: AdminStats = {
        totalSubscribers: totalSubscribers || 0,
        activeSubscriptions: activeSubscriptions || 0,
        pendingContactForms: pendingContactForms || 0,
        totalRevenue,
        monthlyRevenue,
        newSubscribersThisMonth: newSubscribersThisMonth || 0,
      };

      return { data: stats, error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch admin statistics',
      };
    }
  }

  /**
   * Verify admin access
   */
  async verifyAdminAccess(userId: string): Promise<AdminResponse<boolean>> {
    try {
      const supabase = await this.getSupabase();

      const { data: subscriber } = await supabase
        .from('subscribers')
        .select('role')
        .eq('user_id', userId)
        .single();

      const isAdmin = subscriber?.role === 'admin';

      return { data: isAdmin, error: null };
    } catch (error) {
      return {
        data: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to verify admin access',
      };
    }
  }

  /**
   * Generic paginated query helper
   */
  protected async getPaginatedData<T>(
    tableName: string,
    params: PaginationParams,
    selectQuery: string = '*',
    filters?: Record<string, any>
  ): Promise<AdminResponse<{ data: T[]; count: number }>> {
    try {
      const supabase = await this.getSupabase();

      let query = supabase
        .from(tableName as any)
        .select(selectQuery, { count: 'exact' });

      // Apply filters
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            query = query.eq(key, value);
          }
        });
      }

      // Apply sorting
      if (params.sortBy) {
        query = query.order(params.sortBy, {
          ascending: params.sortOrder === 'asc',
        });
      }

      // Apply pagination
      const from = (params.page - 1) * params.pageSize;
      const to = from + params.pageSize - 1;

      const { data, error, count } = await query.range(from, to);

      if (error) {
        return { data: null, error: error.message };
      }

      return {
        data: { data: data as T[], count: count || 0 },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch data',
      };
    }
  }

  /**
   * Get subscriber growth data for charts (last 6 months)
   */
  async getSubscriberGrowthData(): Promise<
    AdminResponse<Array<{ month: string; subscribers: number }>>
  > {
    try {
      const supabase = await this.getSupabase();
      const months = [];
      const now = new Date();

      for (let i = 5; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthEnd = new Date(
          date.getFullYear(),
          date.getMonth() + 1,
          0,
          23,
          59,
          59
        );

        const { count, error: queryError } = await supabase
          .from('subscribers')
          .select('*', { count: 'exact', head: true })
          .lte('created_at', monthEnd.toISOString());

        if (queryError) {
          throw new Error(`Database query failed: ${queryError.message}`);
        }

        months.push({
          month: date.toLocaleDateString('en-US', { month: 'short' }),
          subscribers: count || 0,
        });
      }

      return { data: months, error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch subscriber growth data',
      };
    }
  }

  /**
   * Get subscription status distribution data
   */
  async getSubscriptionStatusData(): Promise<
    AdminResponse<Array<{ status: string; count: number; fill: string }>>
  > {
    try {
      const supabase = await this.getSupabase();

      const { data: statusCounts, error: queryError } = await supabase
        .from('subscriptions')
        .select('status')
        .not('status', 'is', null);

      if (queryError) {
        console.error('Supabase query error:', queryError);
        throw new Error(`Database query failed: ${queryError.message}`);
      }

      const statusMap = new Map();
      statusCounts?.forEach((item) => {
        const status = item.status || 'unknown';
        statusMap.set(status, (statusMap.get(status) || 0) + 1);
      });

      // If no data, provide default empty data
      if (statusMap.size === 0) {
        return {
          data: [
            { status: 'Active', count: 0, fill: '#22c55e' },
            { status: 'Paused', count: 0, fill: '#f59e0b' },
            { status: 'Cancelled', count: 0, fill: '#ef4444' },
          ],
          error: null,
        };
      }

      const colorMap: Record<string, string> = {
        active: '#22c55e',
        paused: '#f59e0b',
        cancelled: '#ef4444',
        completed: '#6b7280',
      };

      const result = Array.from(statusMap.entries()).map(([status, count]) => ({
        status: status.charAt(0).toUpperCase() + status.slice(1),
        count,
        fill: colorMap[status] || '#6b7280',
      }));

      return { data: result, error: null };
    } catch (error) {
      console.error('Error fetching subscription status data:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch subscription status data',
      };
    }
  }

  /**
   * Get delivery metrics data (last 4 weeks)
   */
  async getDeliveryMetricsData(): Promise<
    AdminResponse<Array<{ week: string; delivered: number; scheduled: number }>>
  > {
    try {
      const supabase = await this.getSupabase();
      const weeks = [];
      const now = new Date();

      for (let i = 3; i >= 0; i--) {
        const weekStart = new Date(now);
        weekStart.setDate(weekStart.getDate() - i * 7);
        weekStart.setHours(0, 0, 0, 0);

        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekEnd.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);

        const { count: delivered, error: deliveredError } = await supabase
          .from('deliveries')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'delivered')
          .gte('delivery_date', weekStart.toISOString())
          .lte('delivery_date', weekEnd.toISOString());

        if (deliveredError) {
          throw new Error(`Database query failed: ${deliveredError.message}`);
        }

        const { count: scheduled, error: scheduledError } = await supabase
          .from('deliveries')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'scheduled')
          .gte('delivery_date', weekStart.toISOString())
          .lte('delivery_date', weekEnd.toISOString());

        if (scheduledError) {
          throw new Error(`Database query failed: ${scheduledError.message}`);
        }

        weeks.push({
          week: weekStart.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
          }),
          delivered: delivered || 0,
          scheduled: scheduled || 0,
        });
      }

      return { data: weeks, error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch delivery metrics data',
      };
    }
  }

  /**
   * Get contact form trends data (last 6 months)
   */
  async getContactFormTrendsData(): Promise<
    AdminResponse<Array<{ month: string; submissions: number }>>
  > {
    try {
      const supabase = await this.getSupabase();
      const months = [];
      const now = new Date();

      for (let i = 5; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthEnd = new Date(
          date.getFullYear(),
          date.getMonth() + 1,
          0,
          23,
          59,
          59
        );

        const { count, error: queryError } = await supabase
          .from('contact_submissions')
          .select('*', { count: 'exact', head: true })
          .lte('created_at', monthEnd.toISOString());

        if (queryError) {
          throw new Error(`Database query failed: ${queryError.message}`);
        }

        months.push({
          month: date.toLocaleDateString('en-US', { month: 'short' }),
          submissions: count || 0,
        });
      }

      return { data: months, error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch contact form trends data',
      };
    }
  }
}

// Create a singleton instance
export const createAdminServerService = () => new AdminServerService();
