import { createServerClient } from '@supabase/ssr';
import { NextResponse, type NextRequest } from 'next/server';

// Define route patterns
const PUBLIC_ROUTES = [
  '/',
  '/about',
  '/contact',
  '/pricing',
  '/auth/verified',
  '/auth/error',
  '/auth/callback',
];

const AUTH_ROUTES = [
  '/login',
  '/signup',
  '/forgot-password',
  '/reset-password',
];

const PROTECTED_ROUTES = {
  USER_AND_ADMIN: ['/dashboard'],
  ADMIN_ONLY: ['/admin'],
};

// Helper function to check if a path matches any pattern
function matchesPattern(pathname: string, patterns: string[]): boolean {
  return patterns.some((pattern) => {
    if (pattern.endsWith('/*')) {
      return pathname.startsWith(pattern.slice(0, -2));
    }
    return pathname === pattern || pathname.startsWith(pattern + '/');
  });
}

// Helper function to get user role from subscribers table
async function getUserRole(supabase: any, user: any): Promise<string | null> {
  try {
    if (!user) return null;

    // Get user role from subscribers table with error handling for RLS issues
    const { data: subscriber, error: subscriberError } = await supabase
      .from('subscribers')
      .select('role')
      .eq('user_id', user.id)
      .single();

    if (subscriberError) {
      // Check if it's an RLS policy error (infinite recursion)
      if (
        subscriberError.code === '42P17' ||
        subscriberError.message?.includes('infinite recursion')
      ) {
        return 'user';
      }

      // For other errors (like record not found), also default to user
      if (subscriberError.code === 'PGRST116') {
        // Record not found - user hasn't completed signup process
        return 'user';
      }

      return 'user';
    }

    return subscriber?.role || 'user';
  } catch {
    return 'user';
  }
}

// Helper function to create redirect response with cookies preserved
function createRedirectWithCookies(
  url: URL,
  supabaseResponse: NextResponse
): NextResponse {
  const redirectResponse = NextResponse.redirect(url);

  // Copy all cookies from the supabase response to maintain session
  supabaseResponse.cookies.getAll().forEach((cookie) => {
    redirectResponse.cookies.set(cookie.name, cookie.value, {
      path: cookie.path,
      domain: cookie.domain,
      secure: cookie.secure,
      httpOnly: cookie.httpOnly,
      sameSite: cookie.sameSite,
      maxAge: cookie.maxAge,
      expires: cookie.expires,
    });
  });

  return redirectResponse;
}

export async function updateSession(request: NextRequest) {
  const { pathname } = request.nextUrl;

  const supabaseResponse = NextResponse.next({
    request,
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            request.cookies.set(name, value);
            supabaseResponse.cookies.set(name, value, options);
          });
        },
      },
    }
  );

  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Skip middleware logic for API routes and static files
  if (pathname.startsWith('/api/') || pathname.startsWith('/_next/')) {
    return supabaseResponse;
  }

  // Get user role information - this makes a database query on EVERY request
  const role = await getUserRole(supabase, user);

  // Check if route is public (no authentication required)
  if (matchesPattern(pathname, PUBLIC_ROUTES)) {
    return supabaseResponse;
  }

  // Handle auth routes (signin, signup, etc.)
  if (matchesPattern(pathname, AUTH_ROUTES)) {
    // If user is already authenticated, redirect to appropriate dashboard
    if (user) {
      // Get role or default to 'user' if role fetch fails
      const userRole = role || 'user';
      const redirectUrl =
        userRole === 'admin'
          ? new URL('/admin', request.url)
          : new URL('/dashboard', request.url);

      return createRedirectWithCookies(redirectUrl, supabaseResponse);
    }

    return supabaseResponse;
  }

  // Handle protected routes
  const isUserAndAdminRoute = matchesPattern(
    pathname,
    PROTECTED_ROUTES.USER_AND_ADMIN
  );
  const isAdminOnlyRoute = matchesPattern(
    pathname,
    PROTECTED_ROUTES.ADMIN_ONLY
  );

  if (isUserAndAdminRoute || isAdminOnlyRoute) {
    // Check if user is authenticated
    if (!user) {
      const signInUrl = new URL('/login', request.url);
      signInUrl.searchParams.set('redirectTo', pathname);
      return createRedirectWithCookies(signInUrl, supabaseResponse);
    }

    // Check role-based access
    if (isAdminOnlyRoute && role !== 'admin') {
      // User trying to access admin route - redirect to user dashboard
      const dashboardUrl = new URL('/dashboard', request.url);
      return createRedirectWithCookies(dashboardUrl, supabaseResponse);
    }

    return supabaseResponse;
  }

  return supabaseResponse;
}
