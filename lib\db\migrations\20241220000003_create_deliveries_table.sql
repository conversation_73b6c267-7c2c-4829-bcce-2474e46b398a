-- Create deliveries table for tracking delivery schedules
CREATE TABLE IF NOT EXISTS deliveries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subscription_id UUID REFERENCES subscriptions(id) ON DELETE CASCADE,
    delivery_date DATE NOT NULL,
    pickup_location TEXT NOT NULL,
    box_contents TEXT,
    special_instructions TEXT,
    status TEXT NOT NULL DEFAULT 'scheduled',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add check constraint for delivery status
ALTER TABLE deliveries 
ADD CONSTRAINT deliveries_status_check 
CHECK (status IN ('scheduled', 'delivered', 'cancelled'));

-- Add check constraint for pickup locations
ALTER TABLE deliveries 
ADD CONSTRAINT deliveries_pickup_location_check 
CHECK (pickup_location IN ('elite_bodies', 'shabach_ministries'));

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_deliveries_subscription_id ON deliveries(subscription_id);
CREATE INDEX IF NOT EXISTS idx_deliveries_delivery_date ON deliveries(delivery_date);
CREATE INDEX IF NOT EXISTS idx_deliveries_status ON deliveries(status);
CREATE INDEX IF NOT EXISTS idx_deliveries_pickup_location ON deliveries(pickup_location);

-- Enable RLS for deliveries
ALTER TABLE deliveries ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for deliveries (users can only see their own deliveries)
CREATE POLICY deliveries_access ON deliveries
    FOR ALL
    USING (EXISTS (
        SELECT 1 FROM subscriptions
        JOIN subscribers ON subscribers.id = subscriptions.subscriber_id
        WHERE subscriptions.id = deliveries.subscription_id
        AND subscribers.user_id = auth.uid()
    ));

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_deliveries_updated_at 
    BEFORE UPDATE ON deliveries 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
