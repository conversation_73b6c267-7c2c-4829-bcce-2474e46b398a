'use client';

import SubscriptionWizard from '@/components/subscription/SubscriptionWizard';

export function GuestSubscriptionWizard() {
  return (
    <SubscriptionWizard
      onCancel={() => (window.location.href = '/')}
      subscriberId={null}
      user={null}
    />
  );
}

export function AuthenticatedSubscriptionWizard({
  subscriber,
  user,
}: {
  subscriber: any;
  user: any;
}) {
  return (
    <SubscriptionWizard
      onCancel={() => (window.location.href = '/')}
      subscriberId={subscriber?.id || null}
      user={user}
    />
  );
}
