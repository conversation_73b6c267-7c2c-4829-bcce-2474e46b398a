import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { authServer } from '@/lib/utils/auth-server';
import { getAdminStats } from '@/lib/services/server/admin-server';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, Package, MessageSquare, DollarSign } from 'lucide-react';
import { AdminCharts } from '@/components/admin';
import { AdminStatsSkeleton, ChartSkeleton } from '@/components/ui/skeletons';
import { PPRErrorBoundary } from '@/components/ui/ppr-error-boundary';

// Enable Partial Prerendering for this page (when Next.js supports it)
// export const experimental_ppr = true;

// Dynamic components for PPR
async function AdminWelcome() {
  const { data: user } = await authServer.getCurrentUser();

  if (!user) {
    redirect('/login');
  }

  if (user.role !== 'admin') {
    redirect('/dashboard');
  }

  return (
    <div>
      <h1 className='text-2xl md:text-3xl font-bold text-gray-900'>
        Welcome back, {user.name}!
      </h1>
      <p className='text-gray-600 mt-2 text-sm md:text-base'>
        Here&apos;s what&apos;s happening with your AsedaFoods business today.
      </p>
    </div>
  );
}

async function AdminStats() {
  const { data: user } = await authServer.getCurrentUser();

  if (!user || user.role !== 'admin') {
    redirect('/login');
  }

  const stats = await getAdminStats();

  if (!stats) {
    return <div>No statistics available</div>;
  }

  return (
    <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6'>
      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>
            Total Subscribers
          </CardTitle>
          <Users className='h-4 w-4 text-muted-foreground' />
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold'>{stats.totalSubscribers}</div>
          <p className='text-xs text-muted-foreground'>
            +{stats.newSubscribersThisMonth} this month
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>
            Active Subscriptions
          </CardTitle>
          <Package className='h-4 w-4 text-muted-foreground' />
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold'>{stats.activeSubscriptions}</div>
          <p className='text-xs text-muted-foreground'>Currently active</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>Monthly Revenue</CardTitle>
          <DollarSign className='h-4 w-4 text-muted-foreground' />
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold'>
            ${(stats.monthlyRevenue / 100).toFixed(2)}
          </div>
          <p className='text-xs text-muted-foreground'>This month</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>
            Pending Messages
          </CardTitle>
          <MessageSquare className='h-4 w-4 text-muted-foreground' />
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold'>{stats.pendingContactForms}</div>
          <p className='text-xs text-muted-foreground'>Need attention</p>
        </CardContent>
      </Card>
    </div>
  );
}

export default function AdminPage() {
  return (
    <div className='space-y-4 md:space-y-6'>
      {/* Static welcome section structure with dynamic content */}
      <PPRErrorBoundary>
        <Suspense
          fallback={
            <div>
              <div className='h-8 bg-gray-200 rounded w-64 mb-2 animate-pulse'></div>
              <div className='h-4 bg-gray-200 rounded w-96 animate-pulse'></div>
            </div>
          }
        >
          <AdminWelcome />
        </Suspense>
      </PPRErrorBoundary>

      {/* Dynamic statistics cards */}
      <PPRErrorBoundary>
        <Suspense fallback={<AdminStatsSkeleton />}>
          <AdminStats />
        </Suspense>
      </PPRErrorBoundary>

      {/* Static analytics section with dynamic charts */}
      <div>
        <h2 className='text-lg md:text-xl font-semibold text-gray-900 mb-3 md:mb-4'>
          Business Analytics
        </h2>
        <PPRErrorBoundary>
          <Suspense fallback={<ChartSkeleton />}>
            <AdminCharts />
          </Suspense>
        </PPRErrorBoundary>
      </div>
    </div>
  );
}
