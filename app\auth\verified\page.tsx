'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { CheckCircle, ArrowRight, Mail } from 'lucide-react';
import { Button } from '@/components/ui/button';

function VerifiedContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams.get('email');
  const redirectPath = searchParams.get('redirect') || '/dashboard';
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    // Auto-redirect to appropriate destination after 5 seconds
    const timer = setInterval(() => {
      setCountdown((prev) => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Separate effect to handle navigation when countdown reaches 0
  useEffect(() => {
    if (countdown === 0) {
      router.push(redirectPath);
    }
  }, [countdown, router, redirectPath]);

  const handleContinue = () => {
    router.push(redirectPath);
  };

  // Determine destination name for display
  const destinationName =
    redirectPath === '/admin' ? 'Admin Dashboard' : 'Dashboard';

  return (
    <div className='min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8'>
      <div className='max-w-md w-full space-y-8'>
        <div className='border border-green-200 bg-green-50 rounded-lg shadow-sm'>
          <header className='text-center p-6'>
            <div className='mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4'>
              <CheckCircle className='h-8 w-8 text-green-600' />
            </div>
            <h1 className='text-2xl font-bold text-green-900'>
              Email Verified Successfully!
            </h1>
            <p className='text-green-700 mt-2'>
              Your account has been verified and you&apos;re now signed in.
            </p>
          </header>

          <div className='space-y-6 p-6 pt-0'>
            {email && (
              <div className='flex items-center space-x-3 p-3 bg-white rounded-lg border border-green-200'>
                <Mail className='h-5 w-5 text-green-600' />
                <div>
                  <p className='text-sm font-medium text-gray-900'>
                    Verified Email
                  </p>
                  <p className='text-sm text-gray-600'>{email}</p>
                </div>
              </div>
            )}

            <div className='space-y-4'>
              <Button
                onClick={handleContinue}
                className='w-full bg-green-600 hover:bg-green-700 text-white'
              >
                Continue to {destinationName}
                <ArrowRight className='ml-2 h-4 w-4' />
              </Button>

              <p className='text-center text-sm text-gray-600'>
                Redirecting to {destinationName.toLowerCase()} automatically in{' '}
                {countdown} second
                {countdown !== 1 ? 's' : ''}...
              </p>
            </div>

            <div className='bg-white p-4 rounded-lg border border-green-200'>
              <h3 className='text-sm font-medium text-gray-900 mb-2'>
                What&apos;s Next?
              </h3>
              <ul className='text-sm text-gray-600 space-y-1'>
                {redirectPath === '/admin' ? (
                  <>
                    <li>• Access admin dashboard features</li>
                    <li>• Manage subscribers and subscriptions</li>
                    <li>• Review contact forms and analytics</li>
                  </>
                ) : (
                  <>
                    <li>• Complete your profile setup</li>
                    <li>• Browse our fresh produce boxes</li>
                    <li>• Set up your first subscription</li>
                  </>
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function VerifiedPage() {
  return (
    <Suspense
      fallback={
        <div className='min-h-screen flex items-center justify-center bg-gray-50'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-green-600'></div>
        </div>
      }
    >
      <VerifiedContent />
    </Suspense>
  );
}
