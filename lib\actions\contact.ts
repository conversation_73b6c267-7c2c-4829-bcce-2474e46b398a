'use server';

import { createClient } from '@/lib/supabase/server';
import { EmailService } from '@/lib/services/email';
import { z } from 'zod';

// Validation schema for contact form
const contactFormSchema = z.object({
  name: z
    .string()
    .min(1, 'Name is required')
    .max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Please enter a valid email address'),
  message: z
    .string()
    .min(10, 'Message must be at least 10 characters')
    .max(2000, 'Message must be less than 2000 characters'),
});

export type ContactFormData = z.infer<typeof contactFormSchema>;

export async function submitContactFormAction(formData: ContactFormData) {
  try {
    // Validate the data
    const validatedData = contactFormSchema.parse(formData);

    // Create Supabase client (no auth required for contact form)
    const supabase = await createClient();

    // Insert contact submission into database
    const { data: contactSubmission, error: insertError } = await supabase
      .from('contact_submissions')
      .insert({
        name: validatedData.name,
        email: validatedData.email,
        message: validatedData.message,
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error inserting contact submission:', insertError);
      return {
        success: false,
        error: 'Failed to submit contact form. Please try again.',
      };
    }

    // Send admin notification email
    try {
      const adminEmail = process.env.ADMIN_EMAIL;
      if (adminEmail && contactSubmission) {
        await EmailService.sendContactFormNotification(adminEmail, {
          id: contactSubmission.id,
          name: contactSubmission.name,
          email: contactSubmission.email,
          message: contactSubmission.message,
          submittedAt: new Date(contactSubmission.created_at ?? Date.now()),
        });
        console.log('Contact form notification email sent to admin');
      }
    } catch (emailError) {
      // Don't fail the submission if email sending fails
      console.error(
        'Error sending contact form notification email:',
        emailError
      );
    }

    return {
      success: true,
      message: "Thank you for your message! We'll get back to you soon.",
      data: {
        id: contactSubmission.id,
        submittedAt: contactSubmission.created_at,
      },
    };
  } catch (error) {
    console.error('Contact form submission error:', error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error:
          error.errors[0]?.message || 'Please check your input and try again.',
      };
    }

    return {
      success: false,
      error: 'An unexpected error occurred. Please try again.',
    };
  }
}
