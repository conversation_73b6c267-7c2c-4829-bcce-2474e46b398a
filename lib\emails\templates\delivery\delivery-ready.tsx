import {
  Body,
  Button,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';

interface DeliveryReadyProps {
  subscriberName: string;
  deliveryData: {
    deliveryDate: Date;
    pickupLocation: string;
    pickupHours: string;
    boxSize: string;
    boxContents?: string[];
    specialInstructions?: string;
    deliveryId: string;
  };
}

const baseUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : 'http://localhost:3000';

export const DeliveryReady = ({
  subscriberName = 'Valued Customer',
  deliveryData = {
    deliveryDate: new Date(),
    pickupLocation: 'shabach_ministries',
    pickupHours: '9:00 AM - 5:00 PM',
    boxSize: 'medium',
    boxContents: ['Fresh seasonal vegetables', 'Organic fruits', 'Local herbs'],
    specialInstructions: 'Please bring your own bag',
    deliveryId: 'delivery-123',
  },
}: DeliveryReadyProps) => {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatLocation = (location: string) => {
    return location.replace('_', ' ').toUpperCase();
  };

  return (
    <Html>
      <Head />
      <Preview>Your fresh box is ready for pickup! 📦</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Img
              src={`${baseUrl}/logo.png`}
              width='120'
              height='36'
              alt='AsedaFoods'
              style={logo}
            />
          </Section>

          <Heading style={h1}>Your Fresh Box is Ready! 📦</Heading>

          <Text style={text}>Hi {subscriberName},</Text>

          <Text style={text}>
            Great news! Your fresh produce box is ready for pickup. Here are the
            details:
          </Text>

          <Section style={deliveryCard}>
            <Heading style={h2}>Pickup Details</Heading>

            <div style={detailRow}>
              <strong>📅 Pickup Date:</strong>{' '}
              {formatDate(deliveryData.deliveryDate)}
            </div>

            <div style={detailRow}>
              <strong>📍 Location:</strong>{' '}
              {formatLocation(deliveryData.pickupLocation)}
            </div>

            <div style={detailRow}>
              <strong>🕒 Hours:</strong> {deliveryData.pickupHours}
            </div>

            <div style={detailRow}>
              <strong>📦 Box Size:</strong>{' '}
              {deliveryData.boxSize.charAt(0).toUpperCase() +
                deliveryData.boxSize.slice(1)}
            </div>
          </Section>

          {deliveryData.boxContents && deliveryData.boxContents.length > 0 && (
            <Section style={contentsCard}>
              <Heading style={h2}>What&apos;s in Your Box</Heading>
              <ul style={contentsList}>
                {deliveryData.boxContents.map((item, index) => (
                  <li key={index} style={contentsItem}>
                    🌱 {item}
                  </li>
                ))}
              </ul>
            </Section>
          )}

          {deliveryData.specialInstructions && (
            <Section style={instructionsCard}>
              <Heading style={h2}>Special Instructions</Heading>
              <Text style={instructionsText}>
                💡 {deliveryData.specialInstructions}
              </Text>
            </Section>
          )}

          <Section style={buttonContainer}>
            <Button
              style={button}
              href={`${baseUrl}/dashboard/deliveries/${deliveryData.deliveryId}`}
            >
              View Delivery Details
            </Button>
          </Section>

          <Text style={text}>
            Please make sure to pick up your box during the specified hours. If
            you have any questions or need to reschedule, please contact us as
            soon as possible.
          </Text>

          <Hr style={hr} />

          <Text style={footer}>
            Thank you for supporting local farmers and choosing AsedaFoods! 🌱
          </Text>

          <Text style={footer}>
            Need help? Reply to this email or visit our{' '}
            <Link href={`${baseUrl}/contact`} style={link}>
              contact page
            </Link>
            .
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

export default DeliveryReady;

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
};

const logoContainer = {
  margin: '32px 0',
  textAlign: 'center' as const,
};

const logo = {
  margin: '0 auto',
};

const h1 = {
  color: '#333',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0',
  padding: '0',
  textAlign: 'center' as const,
};

const h2 = {
  color: '#333',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
  padding: '0',
};

const text = {
  color: '#333',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '16px',
  lineHeight: '26px',
  margin: '16px 32px',
};

const deliveryCard = {
  backgroundColor: '#f8f9fa',
  border: '1px solid #e9ecef',
  borderRadius: '8px',
  margin: '24px 32px',
  padding: '24px',
};

const contentsCard = {
  backgroundColor: '#f0f9ff',
  border: '1px solid #bae6fd',
  borderRadius: '8px',
  margin: '24px 32px',
  padding: '24px',
};

const instructionsCard = {
  backgroundColor: '#fef3c7',
  border: '1px solid #fbbf24',
  borderRadius: '8px',
  margin: '24px 32px',
  padding: '24px',
};

const detailRow = {
  margin: '12px 0',
  fontSize: '16px',
  lineHeight: '24px',
};

const contentsList = {
  margin: '0',
  padding: '0 0 0 16px',
};

const contentsItem = {
  margin: '8px 0',
  fontSize: '16px',
  lineHeight: '24px',
};

const instructionsText = {
  color: '#92400e',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0',
};

const buttonContainer = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const button = {
  backgroundColor: '#22c55e',
  borderRadius: '6px',
  color: '#fff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '16px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '200px',
  padding: '12px 0',
  margin: '0 auto',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '32px 0',
};

const footer = {
  color: '#8898aa',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  fontSize: '14px',
  lineHeight: '24px',
  margin: '16px 32px',
  textAlign: 'center' as const,
};

const link = {
  color: '#22c55e',
  textDecoration: 'underline',
};
