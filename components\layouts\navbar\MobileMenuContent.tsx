'use client';

import Link from 'next/link';
import { buttonVariants } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useMobileMenu } from './MobileMenuContext';

const navLink = [
  { id: 1, title: 'About Us', url: '#aboutus' },
  { id: 3, title: 'How It works', url: '#howitworks' },
  { id: 4, title: 'Plans', url: '#subscriptionplans' },
  { id: 5, title: 'Seasonal Produce', url: '#freshselection' },
  { id: 6, title: 'FAQs', url: '#faqs' },
];

interface MobileMenuContentProps {
  user?: {
    id: string;
    email: string;
    name?: string;
    role?: string;
  } | null;
}

export function MobileMenuContent({ user }: MobileMenuContentProps) {
  const { isOpen, close } = useMobileMenu();

  // Determine dashboard URL based on user role
  const getDashboardUrl = () => {
    if (user?.role === 'admin') {
      return '/admin';
    }
    return '/dashboard';
  };

  if (!isOpen) return null;

  return (
    <div className="md:hidden flex flex-col space-y-4 p-4 border-b border-green-50 bg-white">
      {navLink.map((link) => (
        <Link
          key={link.id}
          href={link.url}
          className="font-medium text-neutral-500 hover:text-green-600 hover:underline"
          onClick={close}
        >
          {link.title}
        </Link>
      ))}

      {/* Mobile authentication buttons */}
      <div className="pt-4 border-t border-green-100">
        {user ? (
          // Authenticated user - show dashboard button
          <Link
            href={getDashboardUrl()}
            className={cn(
              buttonVariants({ variant: 'default' }),
              'w-full justify-center'
            )}
            onClick={close}
          >
            {user.role === 'admin' ? 'Admin Dashboard' : 'Dashboard'}
          </Link>
        ) : (
          // Unauthenticated user - show subscription button only on mobile
          <Link
            href={'/get-a-box'}
            className={cn(
              buttonVariants({ variant: 'default' }),
              'w-full justify-center'
            )}
            onClick={close}
          >
            Start a Subscription
          </Link>
        )}
      </div>
    </div>
  );
}
