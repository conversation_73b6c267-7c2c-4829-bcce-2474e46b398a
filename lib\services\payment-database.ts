import { createClient } from '@/lib/supabase/server';
import { Payment, PaymentInsert, PaymentStatus, PaymentProvider } from '@/lib/types/payment';

export class PaymentDatabaseService {
  private async getSupabase() {
    return await createClient();
  }

  /**
   * Create a payment record
   */
  async createPayment(
    subscriptionId: string,
    paymentId: string,
    amount: number,
    status: PaymentStatus,
    provider: PaymentProvider = 'stripe'
  ): Promise<{ data: Payment | null; error: any }> {
    try {
      const supabase = await this.getSupabase();
      
      const paymentInsert: PaymentInsert = {
        subscription_id: subscriptionId,
        payment_id: paymentId,
        payment_provider: provider,
        amount: amount / 100, // Convert cents to dollars
        status,
      };

      const { data, error } = await supabase
        .from('payments')
        .insert(paymentInsert)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      console.error('Error creating payment record:', error);
      return { data: null, error };
    }
  }

  /**
   * Update payment status
   */
  async updatePaymentStatus(
    paymentId: string,
    status: PaymentStatus
  ): Promise<{ data: Payment | null; error: any }> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('payments')
        .update({ status })
        .eq('payment_id', paymentId)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      console.error('Error updating payment status:', error);
      return { data: null, error };
    }
  }

  /**
   * Get payment by payment ID
   */
  async getPaymentByPaymentId(
    paymentId: string
  ): Promise<{ data: Payment | null; error: any }> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('payments')
        .select('*')
        .eq('payment_id', paymentId)
        .single();

      return { data, error };
    } catch (error) {
      console.error('Error getting payment by payment ID:', error);
      return { data: null, error };
    }
  }

  /**
   * Get payments for a subscription
   */
  async getPaymentsBySubscription(
    subscriptionId: string
  ): Promise<{ data: Payment[] | null; error: any }> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('payments')
        .select('*')
        .eq('subscription_id', subscriptionId)
        .order('created_at', { ascending: false });

      return { data, error };
    } catch (error) {
      console.error('Error getting payments by subscription:', error);
      return { data: null, error };
    }
  }

  /**
   * Get payments for a subscriber
   */
  async getPaymentsBySubscriber(
    subscriberId: string
  ): Promise<{ data: Payment[] | null; error: any }> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('payments')
        .select(`
          *,
          subscriptions!inner(
            subscriber_id
          )
        `)
        .eq('subscriptions.subscriber_id', subscriberId)
        .order('created_at', { ascending: false });

      return { data, error };
    } catch (error) {
      console.error('Error getting payments by subscriber:', error);
      return { data: null, error };
    }
  }

  /**
   * Get successful payments for a subscription
   */
  async getSuccessfulPayments(
    subscriptionId: string
  ): Promise<{ data: Payment[] | null; error: any }> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('payments')
        .select('*')
        .eq('subscription_id', subscriptionId)
        .eq('status', 'succeeded')
        .order('created_at', { ascending: false });

      return { data, error };
    } catch (error) {
      console.error('Error getting successful payments:', error);
      return { data: null, error };
    }
  }

  /**
   * Check if subscription has successful payment
   */
  async hasSuccessfulPayment(
    subscriptionId: string
  ): Promise<{ data: boolean; error: any }> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('payments')
        .select('id')
        .eq('subscription_id', subscriptionId)
        .eq('status', 'succeeded')
        .limit(1);

      if (error) {
        return { data: false, error };
      }

      return { data: data.length > 0, error: null };
    } catch (error) {
      console.error('Error checking successful payment:', error);
      return { data: false, error };
    }
  }

  /**
   * Delete payment record
   */
  async deletePayment(
    paymentId: string
  ): Promise<{ data: boolean; error: any }> {
    try {
      const supabase = await this.getSupabase();

      const { error } = await supabase
        .from('payments')
        .delete()
        .eq('payment_id', paymentId);

      return { data: !error, error };
    } catch (error) {
      console.error('Error deleting payment:', error);
      return { data: false, error };
    }
  }
}

// Export singleton instance
export const paymentDatabaseService = new PaymentDatabaseService();
