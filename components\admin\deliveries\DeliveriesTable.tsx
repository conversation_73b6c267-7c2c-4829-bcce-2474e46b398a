'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Clock,
  Package,
} from 'lucide-react';
import {
  getDeliveriesAction,
  updateDeliveryStatusAction,
} from '@/lib/actions/admin/deliveries';
import { DeliveryWithSubscription } from '@/lib/types/admin';
import { DELIVERY_STATUS } from '@/lib/types/delivery';

export function DeliveriesTable() {
  const [deliveries, setDeliveries] = useState<DeliveryWithSubscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const pageSize = 10;

  const fetchDeliveries = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await getDeliveriesAction({
        page,
        pageSize,
        sortBy: 'delivery_date',
        sortOrder: 'desc',
      });

      if (!result.success || result.error) {
        setError(result.error || 'Failed to fetch deliveries');
      } else if (result.data) {
        setDeliveries(result.data.data);
        setTotalCount(result.data.count);
      }
    } catch {
      setError('Failed to fetch deliveries');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDeliveries();
  }, [page]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleStatusUpdate = async (
    deliveryId: string,
    newStatus: 'scheduled' | 'delivered' | 'cancelled'
  ) => {
    try {
      const result = await updateDeliveryStatusAction(deliveryId, newStatus);

      if (result.success) {
        // Refresh the deliveries list
        fetchDeliveries();
      } else {
        setError(result.error || 'Failed to update delivery status');
      }
    } catch {
      setError('Failed to update delivery status');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig =
      DELIVERY_STATUS[status as keyof typeof DELIVERY_STATUS];
    if (!statusConfig) return null;

    const variants = {
      scheduled: 'secondary' as const,
      delivered: 'default' as const,
      cancelled: 'destructive' as const,
    };

    const colors = {
      scheduled: 'bg-yellow-100 text-yellow-800',
      delivered: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800',
    };

    return (
      <Badge
        variant={variants[status as keyof typeof variants] || 'default'}
        className={colors[status as keyof typeof colors] || ''}
      >
        {statusConfig.name}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <div className='space-y-4'>
        <div className='animate-pulse'>
          <div className='h-4 bg-gray-200 rounded w-1/4 mb-4'></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className='h-12 bg-gray-200 rounded mb-2'></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='text-center py-8'>
        <XCircle className='h-8 w-8 text-red-500 mx-auto mb-2' />
        <p className='text-red-600'>{error}</p>
        <Button onClick={fetchDeliveries} variant='outline' className='mt-4'>
          Try Again
        </Button>
      </div>
    );
  }

  const totalPages = Math.ceil(totalCount / pageSize);

  return (
    <div className='space-y-4'>
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Delivery Date</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Box Size</TableHead>
              <TableHead>Pickup Location</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className='text-right'>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {deliveries.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className='text-center py-8'>
                  <Package className='h-8 w-8 text-gray-400 mx-auto mb-2' />
                  <p className='text-gray-500'>No deliveries found</p>
                </TableCell>
              </TableRow>
            ) : (
              deliveries.map((delivery) => (
                <TableRow key={delivery.id}>
                  <TableCell className='font-medium'>
                    {formatDate(delivery.delivery_date)}
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className='font-medium'>
                        {delivery.subscriptions?.subscribers?.name || 'N/A'}
                      </p>
                      <p className='text-sm text-gray-500'>
                        {delivery.subscriptions?.subscribers?.email || 'N/A'}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant='outline'>
                      {delivery.subscriptions?.box_size || 'N/A'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {delivery.pickup_location.replace('_', ' ').toUpperCase()}
                  </TableCell>
                  <TableCell>{getStatusBadge(delivery.status)}</TableCell>
                  <TableCell className='text-right'>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant='ghost' className='h-8 w-8 p-0'>
                          <MoreHorizontal className='h-4 w-4' />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align='end'>
                        {delivery.status !== 'delivered' && (
                          <DropdownMenuItem
                            onClick={() =>
                              handleStatusUpdate(delivery.id, 'delivered')
                            }
                          >
                            <CheckCircle className='mr-2 h-4 w-4' />
                            Mark as Delivered
                          </DropdownMenuItem>
                        )}
                        {delivery.status !== 'scheduled' && (
                          <DropdownMenuItem
                            onClick={() =>
                              handleStatusUpdate(delivery.id, 'scheduled')
                            }
                          >
                            <Clock className='mr-2 h-4 w-4' />
                            Mark as Scheduled
                          </DropdownMenuItem>
                        )}
                        {delivery.status !== 'cancelled' && (
                          <DropdownMenuItem
                            onClick={() =>
                              handleStatusUpdate(delivery.id, 'cancelled')
                            }
                          >
                            <XCircle className='mr-2 h-4 w-4' />
                            Cancel Delivery
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className='flex items-center justify-between'>
          <p className='text-sm text-gray-700'>
            Showing {(page - 1) * pageSize + 1} to{' '}
            {Math.min(page * pageSize, totalCount)} of {totalCount} deliveries
          </p>
          <div className='flex space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
            >
              Previous
            </Button>
            <Button
              variant='outline'
              size='sm'
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
