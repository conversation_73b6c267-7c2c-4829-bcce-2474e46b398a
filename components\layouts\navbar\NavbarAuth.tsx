import { authServer } from '@/lib/utils/auth-server';
import { buttonVariants } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import Link from 'next/link';

/**
 * Server component for navbar authentication state
 * This component fetches user data server-side and renders appropriate buttons
 */
export async function NavbarAuth() {
  const { data: user } = await authServer.getCurrentUser();

  // Determine dashboard URL based on user role
  const getDashboardUrl = () => {
    if (user?.role === 'admin') {
      return '/admin';
    }
    return '/dashboard';
  };

  if (user) {
    // Authenticated user - show dashboard button
    return (
      <Link
        href={getDashboardUrl()}
        className={cn(buttonVariants({ variant: 'default' }), '')}
      >
        {user.role === 'admin' ? 'Admin Dashboard' : 'Dashboard'}
      </Link>
    );
  }

  // Unauthenticated user - show subscription and login buttons
  return (
    <>
      {/* Start Subscription button - always visible */}
      <Link
        href={'/get-a-box'}
        className={cn(buttonVariants({ variant: 'default' }), '')}
      >
        Start a Subscription
      </Link>
      {/* Login button - hidden on mobile */}
      <Link
        href={'/login'}
        className={cn(
          buttonVariants({ variant: 'secondary' }),
          'hidden md:inline-flex'
        )}
      >
        Login
      </Link>
    </>
  );
}

/**
 * Server component for mobile navbar authentication state
 */
export async function MobileNavbarAuth({ onLinkClick }: { onLinkClick: () => void }) {
  const { data: user } = await authServer.getCurrentUser();

  // Determine dashboard URL based on user role
  const getDashboardUrl = () => {
    if (user?.role === 'admin') {
      return '/admin';
    }
    return '/dashboard';
  };

  if (user) {
    // Authenticated user - show dashboard button
    return (
      <Link
        href={getDashboardUrl()}
        className={cn(
          buttonVariants({ variant: 'default' }),
          'w-full justify-center'
        )}
        onClick={onLinkClick}
      >
        {user.role === 'admin' ? 'Admin Dashboard' : 'Dashboard'}
      </Link>
    );
  }

  // Unauthenticated user - show subscription button only on mobile
  return (
    <Link
      href={'/get-a-box'}
      className={cn(
        buttonVariants({ variant: 'default' }),
        'w-full justify-center'
      )}
      onClick={onLinkClick}
    >
      Start a Subscription
    </Link>
  );
}
