import {
  Delivery,
  DeliveryWithSubscription,
  AdminResponse,
  PaginationParams,
} from '@/lib/types/admin';
import { DeliveryFilters, DeliverySummary } from '@/lib/types/delivery';
import { AdminServerService } from '../admin-server';
import { EmailService } from '../email';

export class DeliveriesAdminService extends AdminServerService {
  /**
   * Get paginated deliveries with filters
   */
  async getDeliveries(
    params: PaginationParams,
    filters?: DeliveryFilters
  ): Promise<
    AdminResponse<{ data: DeliveryWithSubscription[]; count: number }>
  > {
    try {
      const supabase = await this.getSupabase();

      let query = supabase.from('deliveries').select(
        `
          *,
          subscriptions (
            id,
            box_size,
            frequency,
            payment_plan,
            subscribers (
              id,
              name,
              email,
              phone
            )
          )
        `,
        { count: 'exact' }
      );

      // Apply filters
      if (filters) {
        if (filters.status) {
          query = query.eq('status', filters.status);
        }
        if (filters.pickupLocation) {
          query = query.eq('pickup_location', filters.pickupLocation);
        }
        if (filters.dateRange) {
          query = query
            .gte(
              'delivery_date',
              filters.dateRange.from.toISOString().split('T')[0]
            )
            .lte(
              'delivery_date',
              filters.dateRange.to.toISOString().split('T')[0]
            );
        }
      }

      // Apply sorting
      if (params.sortBy) {
        query = query.order(params.sortBy, {
          ascending: params.sortOrder === 'asc',
        });
      } else {
        query = query.order('delivery_date', { ascending: false });
      }

      // Apply pagination
      const from = (params.page - 1) * params.pageSize;
      const to = from + params.pageSize - 1;

      const { data, error, count } = await query.range(from, to);

      if (error) {
        return { data: null, error: error.message };
      }

      return {
        data: { data: data as DeliveryWithSubscription[], count: count || 0 },
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to fetch deliveries',
      };
    }
  }

  /**
   * Get delivery by ID with full details
   */
  async getDelivery(
    id: string
  ): Promise<AdminResponse<DeliveryWithSubscription>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('deliveries')
        .select(
          `
          *,
          subscriptions (
            id,
            box_size,
            frequency,
            payment_plan,
            deliveries_remaining,
            subscribers (
              id,
              name,
              email,
              phone,
              address,
              special_instructions,
              allergies
            )
          )
        `
        )
        .eq('id', id)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: data as DeliveryWithSubscription, error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to fetch delivery',
      };
    }
  }

  /**
   * Update delivery status with email notification
   */
  async updateDeliveryStatus(
    id: string,
    status: 'scheduled' | 'delivered' | 'cancelled',
    reason?: string
  ): Promise<AdminResponse<Delivery>> {
    try {
      const supabase = await this.getSupabase();

      // Get delivery details before updating
      const { data: deliveryDetails, error: fetchError } = await supabase
        .from('deliveries')
        .select(
          `
          *,
          subscriptions (
            id,
            box_size,
            frequency,
            subscribers (
              id,
              name,
              email
            )
          )
        `
        )
        .eq('id', id)
        .single();

      if (fetchError || !deliveryDetails) {
        return { data: null, error: 'Delivery not found' };
      }

      // Update delivery status
      const { data, error } = await supabase
        .from('deliveries')
        .update({
          status,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      // Send email notification based on status change
      if (deliveryDetails.subscriptions?.subscribers) {
        const subscriber = deliveryDetails.subscriptions.subscribers;
        const deliveryData = {
          deliveryDate: new Date(deliveryDetails.delivery_date),
          pickupLocation: deliveryDetails.pickup_location,
          boxSize: deliveryDetails.subscriptions.box_size,
          deliveryId: deliveryDetails.id,
        };

        try {
          switch (status) {
            case 'delivered':
              await EmailService.sendDeliveryConfirmed(
                subscriber.email,
                subscriber.name,
                {
                  ...deliveryData,
                  boxContents: deliveryDetails.box_contents
                    ? [deliveryDetails.box_contents]
                    : undefined,
                }
              );
              break;

            case 'cancelled':
              await EmailService.sendDeliveryCancelled(
                subscriber.email,
                subscriber.name,
                {
                  ...deliveryData,
                  cancellationReason: reason,
                }
              );
              break;
          }
        } catch (emailError) {
          console.warn('Failed to send delivery status email:', emailError);
          // Don't fail the status update if email fails
        }
      }

      return { data, error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to update delivery',
      };
    }
  }

  /**
   * Update delivery details
   */
  async updateDelivery(
    id: string,
    updates: Partial<Delivery>
  ): Promise<AdminResponse<Delivery>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('deliveries')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to update delivery',
      };
    }
  }

  /**
   * Get delivery summary statistics
   */
  async getDeliverySummary(
    startDate?: Date,
    endDate?: Date
  ): Promise<AdminResponse<DeliverySummary>> {
    try {
      const supabase = await this.getSupabase();

      let query = supabase.from('deliveries').select('status');

      if (startDate && endDate) {
        query = query
          .gte('delivery_date', startDate.toISOString().split('T')[0])
          .lte('delivery_date', endDate.toISOString().split('T')[0]);
      }

      const { data: deliveries, error } = await query;

      if (error) {
        return { data: null, error: error.message };
      }

      const totalDeliveries = deliveries?.length || 0;
      const deliveredCount =
        deliveries?.filter((d) => d.status === 'delivered').length || 0;
      const scheduledCount =
        deliveries?.filter((d) => d.status === 'scheduled').length || 0;
      const cancelledCount =
        deliveries?.filter((d) => d.status === 'cancelled').length || 0;
      const deliveryRate =
        totalDeliveries > 0 ? (deliveredCount / totalDeliveries) * 100 : 0;

      const summary: DeliverySummary = {
        totalDeliveries,
        deliveredCount,
        scheduledCount,
        cancelledCount,
        deliveryRate,
      };

      return { data: summary, error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch delivery summary',
      };
    }
  }

  /**
   * Get deliveries for a specific subscription
   */
  async getDeliveriesForSubscription(
    subscriptionId: string
  ): Promise<AdminResponse<Delivery[]>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('deliveries')
        .select('*')
        .eq('subscription_id', subscriptionId)
        .order('delivery_date', { ascending: false });

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to fetch deliveries',
      };
    }
  }

  /**
   * Mark delivery as delivered
   */
  async markAsDelivered(id: string): Promise<AdminResponse<Delivery>> {
    return this.updateDeliveryStatus(id, 'delivered');
  }

  /**
   * Cancel delivery
   */
  async cancelDelivery(id: string): Promise<AdminResponse<Delivery>> {
    return this.updateDeliveryStatus(id, 'cancelled');
  }

  /**
   * Reschedule delivery with email notification
   */
  async rescheduleDelivery(
    id: string,
    newDate: string,
    reason?: string
  ): Promise<AdminResponse<Delivery>> {
    try {
      const supabase = await this.getSupabase();

      // Get delivery details before updating
      const { data: deliveryDetails, error: fetchError } = await supabase
        .from('deliveries')
        .select(
          `
          *,
          subscriptions (
            id,
            box_size,
            frequency,
            subscribers (
              id,
              name,
              email
            )
          )
        `
        )
        .eq('id', id)
        .single();

      if (fetchError || !deliveryDetails) {
        return { data: null, error: 'Delivery not found' };
      }

      const originalDate = new Date(deliveryDetails.delivery_date);

      // Update delivery with new date
      const result = await this.updateDelivery(id, {
        delivery_date: newDate,
        status: 'scheduled',
      });

      if (result.error) {
        return result;
      }

      // Send reschedule email notification
      if (deliveryDetails.subscriptions?.subscribers) {
        const subscriber = deliveryDetails.subscriptions.subscribers;

        try {
          await EmailService.sendDeliveryRescheduled(
            subscriber.email,
            subscriber.name,
            {
              originalDate,
              newDate: new Date(newDate),
              pickupLocation: deliveryDetails.pickup_location,
              boxSize: deliveryDetails.subscriptions.box_size,
              rescheduleReason: reason,
              deliveryId: deliveryDetails.id,
            }
          );
        } catch (emailError) {
          console.warn('Failed to send delivery reschedule email:', emailError);
          // Don't fail the reschedule if email fails
        }
      }

      return result;
    } catch (error) {
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to reschedule delivery',
      };
    }
  }
}

// Create a singleton instance
export const createDeliveriesAdminService = () => new DeliveriesAdminService();
