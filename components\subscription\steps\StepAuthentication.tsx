'use client';

import { User } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { AuthUser } from '@/lib/utils/auth';
import {
  SubscriptionData,
  calculatePrice,
  formatPrice,
} from '@/lib/constants/subscription';
import { Button } from '@/components/ui/button';

interface StepAuthenticationProps {
  user: AuthUser | null;
  subscriptionData: Partial<SubscriptionData>;
}

export default function StepAuthentication({
  user,
  subscriptionData,
}: StepAuthenticationProps) {
  const router = useRouter();

  // Calculate pricing for display
  const pricing =
    subscriptionData.boxSize &&
    subscriptionData.frequency &&
    subscriptionData.paymentPlan
      ? calculatePrice(
          subscriptionData.boxSize,
          subscriptionData.frequency,
          subscriptionData.paymentPlan
        )
      : null;

  if (user) {
    return (
      <div className='text-center py-8'>
        <div className='mb-6'>
          <div className='w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4'>
            <User className='w-8 h-8 text-green-600' />
          </div>
          <h3 className='text-xl font-semibold text-gray-900 mb-2'>
            Welcome back!
          </h3>
          <p className='text-gray-600'>
            You&apos;re signed in and ready to complete your subscription.
          </p>
        </div>

        {pricing && (
          <div className='bg-gray-50 rounded-lg p-4 max-w-md mx-auto'>
            <h4 className='font-medium text-gray-900 mb-2'>
              Your Subscription Summary
            </h4>
            <div className='text-sm text-gray-600 space-y-1'>
              <div>Box Size: {subscriptionData.boxSize}</div>
              <div>Frequency: {subscriptionData.frequency}</div>
              <div>Payment Plan: {subscriptionData.paymentPlan}</div>
              <div className='font-semibold text-lg text-gray-900 mt-2'>
                Total: {formatPrice(pricing.totalPrice)}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className='max-w-2xl mx-auto'>
      {pricing && (
        <div className='bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6'>
          <h4 className='font-medium text-blue-900 mb-2'>Your Selected Plan</h4>
          <div className='text-sm text-blue-800 space-y-1'>
            <div>Box Size: {subscriptionData.boxSize}</div>
            <div>Frequency: {subscriptionData.frequency}</div>
            <div>Payment Plan: {subscriptionData.paymentPlan}</div>
            <div className='font-semibold text-lg mt-2'>
              Total: {formatPrice(pricing.totalPrice)}
            </div>
          </div>
        </div>
      )}

      <div className='h-full flex items-center justify-center'>
        <div className='max-w-md mx-auto text-center p-4'>
          <h1 className='text-2xl font-bold text-gray-900 mb-4'>
            Sign In Required
          </h1>
          <p className='text-gray-600 mb-6'>
            Please sign in to create a subscription and start receiving fresh,
            local produce.
          </p>
          <div className='space-y-3 flex flex-col max-w-[300px] mx-auto items-center justify-center'>
            <Button onClick={() => router.push('/login')} className='w-full'>
              Sign In
            </Button>
            <Button
              variant={'outline'}
              onClick={() => router.push('/signup')}
              className='w-full'
            >
              Create Account
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
