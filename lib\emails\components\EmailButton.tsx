import { Button } from '@react-email/components';

interface EmailButtonProps {
  href: string;
  text: string;
  style?: React.CSSProperties;
}

export function EmailButton({ href, text, style = {} }: EmailButtonProps) {
  return (
    <Button
      href={href}
      style={{
        backgroundColor: '#16a34a',
        color: '#ffffff',
        padding: '12px 24px',
        borderRadius: '6px',
        textDecoration: 'none',
        fontWeight: 'bold',
        fontSize: '16px',
        border: 'none',
        cursor: 'pointer',
        display: 'inline-block',
        margin: '20px 0',
        ...style,
      }}
    >
      {text}
    </Button>
  );
}
