#!/usr/bin/env node

/**
 * Stripe Configuration Test Script
 * 
 * This script tests the Stripe configuration utility to ensure
 * it's working correctly in different environments.
 * 
 * Usage:
 *   node scripts/test-stripe-config.js
 *   NODE_ENV=production node scripts/test-stripe-config.js
 */

// Load environment variables
require('dotenv').config();

// Mock Next.js environment for testing
if (!process.env.NEXT_PUBLIC_ENVIRONMENT) {
  process.env.NEXT_PUBLIC_ENVIRONMENT = process.env.NODE_ENV === 'production' ? 'production' : 'development';
}

console.log('🧪 Testing Stripe Configuration...\n');

// Test environment detection
console.log('📍 Environment Detection:');
console.log('  NODE_ENV:', process.env.NODE_ENV || 'undefined');
console.log('  NEXT_PUBLIC_ENVIRONMENT:', process.env.NEXT_PUBLIC_ENVIRONMENT || 'undefined');

// Test if we can load the utility (this will test the import path)
let stripeConfigUtils;
try {
  // We need to use a relative path since this is a script
  const path = require('path');
  const utilPath = path.join(__dirname, '../lib/utils/stripe-config.ts');
  
  // For testing purposes, we'll simulate the utility logic
  const isDevelopment = () => {
    return (
      process.env.NODE_ENV === 'development' || 
      process.env.NODE_ENV === 'test' ||
      process.env.NEXT_PUBLIC_ENVIRONMENT === 'development'
    );
  };

  const isProduction = () => {
    return process.env.NODE_ENV === 'production' && 
           process.env.NEXT_PUBLIC_ENVIRONMENT !== 'development';
  };

  console.log('  Detected as development:', isDevelopment());
  console.log('  Detected as production:', isProduction());
  console.log('');

  // Test environment variables
  console.log('🔑 Environment Variables Check:');
  
  const testVars = {
    'STRIPE_SECRET_TEST_KEY': !!process.env.STRIPE_SECRET_TEST_KEY,
    'NEXT_PUBLIC_STRIPE_PUBLIC_TEST_KEY': !!process.env.NEXT_PUBLIC_STRIPE_PUBLIC_TEST_KEY,
    'STRIPE_SECRET_LIVE_KEY': !!process.env.STRIPE_SECRET_LIVE_KEY,
    'NEXT_PUBLIC_STRIPE_PUBLIC_LIVE_KEY': !!process.env.NEXT_PUBLIC_STRIPE_PUBLIC_LIVE_KEY,
    'STRIPE_WEBHOOK_SECRET_TEST': !!process.env.STRIPE_WEBHOOK_SECRET_TEST,
    'STRIPE_WEBHOOK_SECRET_LIVE': !!process.env.STRIPE_WEBHOOK_SECRET_LIVE,
    'STRIPE_WEBHOOK_SECRET': !!process.env.STRIPE_WEBHOOK_SECRET,
  };

  Object.entries(testVars).forEach(([key, exists]) => {
    const status = exists ? '✅' : '❌';
    console.log(`  ${status} ${key}: ${exists ? 'Set' : 'Not set'}`);
  });

  console.log('');

  // Test key selection logic
  console.log('🎯 Key Selection Logic:');
  
  const currentEnv = isDevelopment() ? 'development' : 'production';
  console.log('  Current environment:', currentEnv);
  
  if (isDevelopment()) {
    console.log('  Should use TEST keys');
    console.log('  Secret key available:', !!process.env.STRIPE_SECRET_TEST_KEY);
    console.log('  Public key available:', !!process.env.NEXT_PUBLIC_STRIPE_PUBLIC_TEST_KEY);
    
    if (process.env.STRIPE_SECRET_TEST_KEY) {
      const key = process.env.STRIPE_SECRET_TEST_KEY;
      console.log('  Secret key format valid:', key.startsWith('sk_test_'));
    }
    
    if (process.env.NEXT_PUBLIC_STRIPE_PUBLIC_TEST_KEY) {
      const key = process.env.NEXT_PUBLIC_STRIPE_PUBLIC_TEST_KEY;
      console.log('  Public key format valid:', key.startsWith('pk_test_'));
    }
  } else {
    console.log('  Should use LIVE keys');
    console.log('  Secret key available:', !!process.env.STRIPE_SECRET_LIVE_KEY);
    console.log('  Public key available:', !!process.env.NEXT_PUBLIC_STRIPE_PUBLIC_LIVE_KEY);
    
    if (process.env.STRIPE_SECRET_LIVE_KEY) {
      const key = process.env.STRIPE_SECRET_LIVE_KEY;
      console.log('  Secret key format valid:', key.startsWith('sk_live_'));
    }
    
    if (process.env.NEXT_PUBLIC_STRIPE_PUBLIC_LIVE_KEY) {
      const key = process.env.NEXT_PUBLIC_STRIPE_PUBLIC_LIVE_KEY;
      console.log('  Public key format valid:', key.startsWith('pk_live_'));
    }
  }

  console.log('');

  // Test validation
  console.log('✅ Validation Results:');
  
  const errors = [];
  
  if (isDevelopment()) {
    if (!process.env.STRIPE_SECRET_TEST_KEY) {
      errors.push('STRIPE_SECRET_TEST_KEY is missing');
    }
    if (!process.env.NEXT_PUBLIC_STRIPE_PUBLIC_TEST_KEY) {
      errors.push('NEXT_PUBLIC_STRIPE_PUBLIC_TEST_KEY is missing');
    }
  } else {
    if (!process.env.STRIPE_SECRET_LIVE_KEY) {
      errors.push('STRIPE_SECRET_LIVE_KEY is missing');
    }
    if (!process.env.NEXT_PUBLIC_STRIPE_PUBLIC_LIVE_KEY) {
      errors.push('NEXT_PUBLIC_STRIPE_PUBLIC_LIVE_KEY is missing');
    }
  }

  if (errors.length === 0) {
    console.log('  ✅ All required environment variables are set');
    console.log('  ✅ Configuration is valid for', currentEnv, 'environment');
  } else {
    console.log('  ❌ Configuration errors found:');
    errors.forEach(error => console.log('    -', error));
  }

  console.log('');
  console.log('🎉 Stripe configuration test completed!');

} catch (error) {
  console.error('❌ Error testing Stripe configuration:', error.message);
  process.exit(1);
}
