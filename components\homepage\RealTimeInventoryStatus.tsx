import { unstable_noStore } from 'next/cache';
import { createClient } from '@/lib/supabase/server';
import { Package, AlertTriangle, CheckCircle, Clock } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

interface InventoryStatus {
  availableBoxes: number;
  totalCapacity: number;
  lowStock: boolean;
  nextRestockDate: string;
  popularItems: string[];
  weeklyDeliveries: number;
}

async function getCurrentInventoryStatus(): Promise<InventoryStatus> {
  unstable_noStore(); // Opt out of caching for real-time data

  const supabase = await createClient();

  try {
    // Get this week's delivery capacity
    const startOfWeek = new Date();
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);

    // Count scheduled deliveries for this week
    const { count: scheduledDeliveries, error: deliveriesError } =
      await supabase
        .from('deliveries')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'scheduled')
        .gte('delivery_date', startOfWeek.toISOString().split('T')[0])
        .lte('delivery_date', endOfWeek.toISOString().split('T')[0]);

    if (deliveriesError) {
      console.error('Error fetching deliveries:', deliveriesError);
    }

    // Get active subscriptions to estimate capacity
    const { error: subscriptionsError } = await supabase
      .from('subscriptions')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active');

    if (subscriptionsError) {
      console.error('Error fetching subscriptions:', subscriptionsError);
    }

    // Simulate inventory data (in a real app, this would come from an inventory system)
    const totalCapacity = 100; // Weekly capacity
    const currentDeliveries = scheduledDeliveries || 0;
    const availableBoxes = Math.max(0, totalCapacity - currentDeliveries);
    const lowStock = availableBoxes < 20;

    // Calculate next restock date (assuming weekly restocks on Mondays)
    const nextMonday = new Date();
    nextMonday.setDate(
      nextMonday.getDate() + ((1 + 7 - nextMonday.getDay()) % 7)
    );

    return {
      availableBoxes,
      totalCapacity,
      lowStock,
      nextRestockDate: nextMonday.toISOString(),
      popularItems: [
        'Organic Kale',
        'Fresh Tomatoes',
        'Local Carrots',
        'Seasonal Herbs',
        'Mixed Greens',
      ],
      weeklyDeliveries: currentDeliveries,
    };
  } catch (error) {
    console.error('Error fetching inventory status:', error);

    // Return default values on error
    return {
      availableBoxes: 50,
      totalCapacity: 100,
      lowStock: false,
      nextRestockDate: new Date().toISOString(),
      popularItems: ['Fresh Produce'],
      weeklyDeliveries: 0,
    };
  }
}

export async function RealTimeInventoryStatus() {
  const inventory = await getCurrentInventoryStatus();

  const availabilityPercentage =
    (inventory.availableBoxes / inventory.totalCapacity) * 100;

  if (inventory.availableBoxes === 0) {
    return <SoldOutStatus inventory={inventory} />;
  }

  if (inventory.lowStock) {
    return <LowStockStatus inventory={inventory} />;
  }

  return (
    <AvailableStatus
      inventory={inventory}
      availabilityPercentage={availabilityPercentage}
    />
  );
}

function AvailableStatus({
  inventory,
  availabilityPercentage,
}: {
  inventory: InventoryStatus;
  availabilityPercentage: number;
}) {
  return (
    <section className='py-8 bg-green-50 border-l-4 border-green-400'>
      <div className='max-w-7xl mx-auto px-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center space-x-4'>
            <div className='flex-shrink-0'>
              <CheckCircle className='h-8 w-8 text-green-600' />
            </div>
            <div>
              <h3 className='text-lg font-semibold text-green-800'>
                Fresh Boxes Available This Week
              </h3>
              <p className='text-green-700'>
                <span className='font-bold'>{inventory.availableBoxes}</span>{' '}
                boxes available for pickup •
                <span className='ml-1'>
                  {Math.round(availabilityPercentage)}% capacity remaining
                </span>
              </p>
              <div className='mt-2'>
                <p className='text-sm text-green-600'>
                  This week&apos;s popular items:{' '}
                  {inventory.popularItems.slice(0, 3).join(', ')}
                </p>
              </div>
            </div>
          </div>
          <div className='hidden md:block'>
            <Button asChild>
              <Link href='/get-a-box'>Order Now</Link>
            </Button>
          </div>
        </div>

        {/* Mobile button */}
        <div className='md:hidden mt-4'>
          <Button asChild className='w-full'>
            <Link href='/get-a-box'>Order Your Box</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}

function LowStockStatus({ inventory }: { inventory: InventoryStatus }) {
  return (
    <section className='py-8 bg-yellow-50 border-l-4 border-yellow-400'>
      <div className='max-w-7xl mx-auto px-4'>
        <Alert className='border-yellow-200 bg-yellow-50'>
          <AlertTriangle className='h-4 w-4 text-yellow-600' />
          <AlertDescription>
            <div className='flex items-center justify-between'>
              <div>
                <h3 className='text-lg font-semibold text-yellow-800 mb-1'>
                  Limited Availability This Week
                </h3>
                <p className='text-yellow-700'>
                  Only{' '}
                  <span className='font-bold'>{inventory.availableBoxes}</span>{' '}
                  boxes remaining for this week&apos;s delivery
                </p>
                <p className='text-sm text-yellow-600 mt-1'>
                  Next restock:{' '}
                  {new Date(inventory.nextRestockDate).toLocaleDateString(
                    'en-US',
                    {
                      weekday: 'long',
                      month: 'long',
                      day: 'numeric',
                    }
                  )}
                </p>
              </div>
              <div className='hidden md:block ml-4'>
                <Badge
                  variant='outline'
                  className='border-yellow-400 text-yellow-800 mb-2'
                >
                  Limited Stock
                </Badge>
                <br />
                <Button asChild size='sm'>
                  <Link href='/get-a-box'>Order Soon</Link>
                </Button>
              </div>
            </div>

            {/* Mobile section */}
            <div className='md:hidden mt-4 flex items-center justify-between'>
              <Badge
                variant='outline'
                className='border-yellow-400 text-yellow-800'
              >
                Limited Stock
              </Badge>
              <Button asChild size='sm'>
                <Link href='/get-a-box'>Order Soon</Link>
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </section>
  );
}

function SoldOutStatus({ inventory }: { inventory: InventoryStatus }) {
  return (
    <section className='py-8 bg-red-50 border-l-4 border-red-400'>
      <div className='max-w-7xl mx-auto px-4'>
        <Alert variant='destructive' className='border-red-200 bg-red-50'>
          <Package className='h-4 w-4' />
          <AlertDescription>
            <div className='text-center'>
              <h3 className='text-lg font-semibold text-red-800 mb-2'>
                This Week&apos;s Boxes Are Sold Out
              </h3>
              <p className='text-red-700 mb-3'>
                We&apos;ve reached our weekly capacity of{' '}
                {inventory.totalCapacity} deliveries. Thank you for the amazing
                response!
              </p>
              <div className='flex items-center justify-center space-x-4 text-sm text-red-600'>
                <div className='flex items-center'>
                  <Clock className='h-4 w-4 mr-1' />
                  Next available:{' '}
                  {new Date(inventory.nextRestockDate).toLocaleDateString(
                    'en-US',
                    {
                      weekday: 'long',
                      month: 'long',
                      day: 'numeric',
                    }
                  )}
                </div>
              </div>
              <div className='mt-4'>
                <Button asChild variant='outline'>
                  <Link href='/get-a-box'>Pre-order for Next Week</Link>
                </Button>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    </section>
  );
}
