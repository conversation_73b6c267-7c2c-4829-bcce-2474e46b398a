// import { createClient } from '@/lib/supabase/server';
import {
  Subscriber,
  SubscriberWithSubscriptions,
  AdminResponse,
  PaginationParams,
  TableFilters,
} from '@/lib/types/admin';
import { AdminServerService } from '../admin-server';

export class SubscribersAdminService extends AdminServerService {
  /**
   * Get paginated subscribers with subscription counts
   */
  async getSubscribers(
    params: PaginationParams,
    filters?: TableFilters
  ): Promise<
    AdminResponse<{ data: SubscriberWithSubscriptions[]; count: number }>
  > {
    try {
      const supabase = await this.getSupabase();

      let query = supabase.from('subscribers').select(
        `
          *,
          subscriptions(*)
        `,
        { count: 'exact' }
      );

      // Apply search filter only if provided and not empty
      if (filters?.search && filters.search.trim() !== '') {
        query = query.or(
          `name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`
        );
      }

      // Apply role filter only if provided and not empty
      if (filters?.role && filters.role.trim() !== '') {
        query = query.eq('role', filters.role);
      }

      // Apply date range filter only if provided
      if (
        filters?.dateRange &&
        filters.dateRange.from &&
        filters.dateRange.to
      ) {
        query = query
          .gte('created_at', filters.dateRange.from.toISOString())
          .lte('created_at', filters.dateRange.to.toISOString());
      }

      // Apply sorting
      if (params.sortBy) {
        query = query.order(params.sortBy, {
          ascending: params.sortOrder === 'asc',
        });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      // Apply pagination
      const from = (params.page - 1) * params.pageSize;
      const to = from + params.pageSize - 1;

      const { data, error, count } = await query.range(from, to);

      if (error) {
        return { data: null, error: error.message };
      }

      // Transform data to include subscription count
      const transformedData =
        data?.map((subscriber) => ({
          ...subscriber,
          subscription_count: subscriber.subscriptions?.length || 0,
        })) || [];

      return {
        data: { data: transformedData, count: count || 0 },
        error: null,
      };
    } catch (error) {
      console.error('Error fetching subscribers:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch subscribers',
      };
    }
  }

  /**
   * Get single subscriber with full details
   */
  async getSubscriber(
    id: string
  ): Promise<AdminResponse<SubscriberWithSubscriptions>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('subscribers')
        .select(
          `
          *,
          subscriptions(*)
        `
        )
        .eq('id', id)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error fetching subscriber:', error);
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to fetch subscriber',
      };
    }
  }

  /**
   * Create new subscriber
   */
  async createSubscriber(
    subscriber: Omit<Subscriber, 'id' | 'created_at'>
  ): Promise<AdminResponse<Subscriber>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('subscribers')
        .insert(subscriber)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error creating subscriber:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to create subscriber',
      };
    }
  }

  /**
   * Update subscriber
   */
  async updateSubscriber(
    id: string,
    updates: Partial<Subscriber>
  ): Promise<AdminResponse<Subscriber>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('subscribers')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error updating subscriber:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to update subscriber',
      };
    }
  }

  /**
   * Delete subscriber (soft delete by deactivating)
   */
  async deleteSubscriber(id: string): Promise<AdminResponse<boolean>> {
    try {
      const supabase = await this.getSupabase();

      // First, cancel all active subscriptions
      await supabase
        .from('subscriptions')
        .update({ status: 'cancelled', is_active: false })
        .eq('subscriber_id', id)
        .eq('status', 'active');

      // Note: We don't actually delete the subscriber record for data integrity
      // Instead, we could add an 'is_active' field to subscribers table
      // For now, we'll just return success

      return { data: true, error: null };
    } catch (error) {
      console.error('Error deleting subscriber:', error);
      return {
        data: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to delete subscriber',
      };
    }
  }

  /**
   * Get subscribers for export (all data, no pagination)
   */
  async getSubscribersForExport(
    filters?: TableFilters
  ): Promise<AdminResponse<Subscriber[]>> {
    try {
      const supabase = await this.getSupabase();

      let query = supabase.from('subscribers').select('*');

      // Apply search filter
      if (filters?.search) {
        query = query.or(
          `name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`
        );
      }

      // Apply role filter
      if (filters?.role) {
        query = query.eq('role', filters.role);
      }

      // Apply date range filter
      if (filters?.dateRange) {
        query = query
          .gte('created_at', filters.dateRange.from.toISOString())
          .lte('created_at', filters.dateRange.to.toISOString());
      }

      const { data, error } = await query.order('created_at', {
        ascending: false,
      });

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: data || [], error: null };
    } catch (error) {
      console.error('Error fetching subscribers for export:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch subscribers for export',
      };
    }
  }
}

// Create a singleton instance
export const createSubscribersAdminService = () =>
  new SubscribersAdminService();
