// import { createClient } from '@/lib/supabase/server';
import {
  Subscription,
  SubscriptionWithSubscriber,
  AdminResponse,
  PaginationParams,
  TableFilters,
} from '@/lib/types/admin';
import { AdminServerService } from '../admin-server';

export class SubscriptionsAdminService extends AdminServerService {
  /**
   * Get paginated subscriptions with subscriber details
   */
  async getSubscriptions(
    params: PaginationParams,
    filters?: TableFilters
  ): Promise<
    AdminResponse<{ data: SubscriptionWithSubscriber[]; count: number }>
  > {
    try {
      const supabase = await this.getSupabase();

      let query = supabase.from('subscriptions').select(
        `
          *,
          subscribers!inner(*)
        `,
        { count: 'exact' }
      );

      // Apply search filter (search by subscriber name or email)
      if (filters?.search) {
        query = query.or(
          `subscribers.name.ilike.%${filters.search}%,subscribers.email.ilike.%${filters.search}%`
        );
      }

      // Apply status filter
      if (filters?.status) {
        query = query.eq('status', filters.status);
      }

      // Apply box size filter
      if (filters?.box_size) {
        query = query.eq('box_size', filters.box_size);
      }

      // Apply frequency filter
      if (filters?.frequency) {
        query = query.eq('frequency', filters.frequency);
      }

      // Apply pickup location filter
      if (filters?.pickup_location) {
        query = query.eq('pickup_location', filters.pickup_location);
      }

      // Apply date range filter
      if (filters?.dateRange) {
        query = query
          .gte('created_at', filters.dateRange.from.toISOString())
          .lte('created_at', filters.dateRange.to.toISOString());
      }

      // Apply sorting
      if (params.sortBy) {
        query = query.order(params.sortBy, {
          ascending: params.sortOrder === 'asc',
        });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      // Apply pagination
      const from = (params.page - 1) * params.pageSize;
      const to = from + params.pageSize - 1;

      const { data, error, count } = await query.range(from, to);

      if (error) {
        return { data: null, error: error.message };
      }

      return {
        data: { data: data || [], count: count || 0 },
        error: null,
      };
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch subscriptions',
      };
    }
  }

  /**
   * Get single subscription with full details
   */
  async getSubscription(
    id: string
  ): Promise<AdminResponse<SubscriptionWithSubscriber>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('subscriptions')
        .select(
          `
          *,
          subscribers(*)
        `
        )
        .eq('id', id)
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data: data as SubscriptionWithSubscriber, error: null };
    } catch (error) {
      console.error('Error fetching subscription:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to fetch subscription',
      };
    }
  }

  /**
   * Update subscription
   */
  async updateSubscription(
    id: string,
    updates: Partial<Subscription>
  ): Promise<AdminResponse<Subscription>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('subscriptions')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error updating subscription:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to update subscription',
      };
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(id: string): Promise<AdminResponse<Subscription>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('subscriptions')
        .update({
          status: 'cancelled',
          is_active: false,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to cancel subscription',
      };
    }
  }

  /**
   * Pause subscription
   */
  async pauseSubscription(
    id: string,
    pauseUntil: string
  ): Promise<AdminResponse<Subscription>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('subscriptions')
        .update({
          status: 'paused',
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      // Create pause request record
      await supabase.from('pause_requests').insert({
        subscription_id: id,
        pause_start_date: new Date().toISOString().split('T')[0],
        pause_end_date: pauseUntil,
      });

      return { data, error: null };
    } catch (error) {
      console.error('Error pausing subscription:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to pause subscription',
      };
    }
  }

  /**
   * Resume subscription
   */
  async resumeSubscription(id: string): Promise<AdminResponse<Subscription>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from('subscriptions')
        .update({
          status: 'active',
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error resuming subscription:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to resume subscription',
      };
    }
  }

  /**
   * Process delivery for subscription
   */
  async processDelivery(id: string): Promise<AdminResponse<Subscription>> {
    try {
      const supabase = await this.getSupabase();

      // Get current subscription
      const { data: subscription, error: fetchError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('id', id)
        .single();

      if (fetchError || !subscription) {
        return { data: null, error: 'Subscription not found' };
      }

      // Mark current delivery as delivered if it exists
      const currentDeliveryDate = subscription.next_delivery_date;
      if (currentDeliveryDate) {
        const { error: deliveryUpdateError } = await supabase
          .from('deliveries')
          .update({ status: 'delivered' })
          .eq('subscription_id', id)
          .eq('delivery_date', currentDeliveryDate)
          .eq('status', 'scheduled');

        if (deliveryUpdateError) {
          console.warn(
            'Failed to update delivery status:',
            deliveryUpdateError
          );
        }
      }

      const deliveriesRemaining = Math.max(
        0,
        (subscription.deliveries_remaining || 0) - 1
      );

      // Calculate next delivery date based on frequency
      const currentDate = new Date(
        subscription.next_delivery_date || new Date()
      );
      const nextDeliveryDate = new Date(currentDate);

      switch (subscription.frequency) {
        case 'weekly':
          nextDeliveryDate.setDate(currentDate.getDate() + 7);
          break;
        case 'biweekly':
          nextDeliveryDate.setDate(currentDate.getDate() + 14);
          break;
        case 'monthly':
          nextDeliveryDate.setMonth(currentDate.getMonth() + 1);
          break;
      }

      const updates: Partial<Subscription> = {
        deliveries_remaining: deliveriesRemaining,
        next_delivery_date: nextDeliveryDate.toISOString().split('T')[0],
        updated_at: new Date().toISOString(),
      };

      // If no deliveries remaining and auto-renew is off, mark as completed
      if (deliveriesRemaining === 0 && !subscription.auto_renew) {
        updates.status = 'completed';
        updates.is_active = false;
      }

      const { data, error } = await supabase
        .from('subscriptions')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message };
      }

      // Create next scheduled delivery if subscription is still active
      if (deliveriesRemaining > 0 && data?.status === 'active') {
        const { data: newDelivery, error: deliveryCreateError } = await supabase
          .from('deliveries')
          .insert({
            subscription_id: id,
            delivery_date: nextDeliveryDate.toISOString().split('T')[0],
            pickup_location: subscription.pickup_location,
            status: 'scheduled',
          })
          .select()
          .single();

        if (deliveryCreateError) {
          console.warn('Failed to create next delivery:', deliveryCreateError);
        } else if (newDelivery) {
          // Send delivery ready email for the new scheduled delivery
          try {
            if (subscription.subscriber_id) {
              const { data: subscriberData } = await supabase
                .from('subscribers')
                .select('name, email')
                .eq('id', subscription.subscriber_id)
                .single();

              if (subscriberData) {
                const { EmailService } = await import('../email');
                await EmailService.sendDeliveryReady(
                  subscriberData.email,
                  subscriberData.name,
                  {
                    deliveryDate: nextDeliveryDate,
                    pickupLocation: subscription.pickup_location,
                    pickupHours: '9:00 AM - 5:00 PM', // Default hours - could be made configurable
                    boxSize: subscription.box_size,
                    deliveryId: newDelivery.id,
                  }
                );
              }
            }
          } catch (emailError) {
            console.warn('Failed to send delivery ready email:', emailError);
          }
        }
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error processing delivery:', error);
      return {
        data: null,
        error:
          error instanceof Error ? error.message : 'Failed to process delivery',
      };
    }
  }

  /**
   * Create initial delivery record for a new subscription
   */
  async createInitialDelivery(
    subscriptionId: string
  ): Promise<AdminResponse<boolean>> {
    try {
      const supabase = await this.getSupabase();

      // Get subscription details
      const { data: subscription, error: fetchError } = await supabase
        .from('subscriptions')
        .select('next_delivery_date, pickup_location')
        .eq('id', subscriptionId)
        .single();

      if (fetchError || !subscription) {
        return { data: false, error: 'Subscription not found' };
      }

      if (!subscription.next_delivery_date) {
        return { data: false, error: 'No delivery date set for subscription' };
      }

      // Create initial delivery record
      const { error: deliveryError } = await supabase
        .from('deliveries')
        .insert({
          subscription_id: subscriptionId,
          delivery_date: subscription.next_delivery_date,
          pickup_location: subscription.pickup_location,
          status: 'scheduled',
        });

      if (deliveryError) {
        return { data: false, error: deliveryError.message };
      }

      return { data: true, error: null };
    } catch (error) {
      console.error('Error creating initial delivery:', error);
      return {
        data: false,
        error:
          error instanceof Error
            ? error.message
            : 'Failed to create initial delivery',
      };
    }
  }
}

// Create a singleton instance
export const createSubscriptionsAdminService = () =>
  new SubscriptionsAdminService();
