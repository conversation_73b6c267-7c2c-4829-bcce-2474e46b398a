'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Root as Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, X, AlertCircle } from 'lucide-react';
import { producePreferencesService } from '@/lib/services/produce-preferences';
import {
  ProduceItemWithPreference,
  PRODUCE_CATEGORIES,
  ProduceCategory,
  MAX_NEVER_SEND_ITEMS,
} from '@/lib/types/produce';
import { useUser } from '@/lib/hooks/useUser';
import { toast } from 'sonner';

interface ProducePreferencesManagerProps {
  className?: string;
}

export default function ProducePreferencesManager({
  className,
}: ProducePreferencesManagerProps) {
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [produceItems, setProduceItems] = useState<ProduceItemWithPreference[]>(
    []
  );
  const [selectedNeverSend, setSelectedNeverSend] = useState<string[]>([]);
  const [activeCategory, setActiveCategory] =
    useState<ProduceCategory>('vegetables');
  const [isEditing, setIsEditing] = useState(false);
  const [hasExistingPreferences, setHasExistingPreferences] = useState(false);

  useEffect(() => {
    if (user) {
      loadProduceItems();
    }
  }, [user, activeCategory]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadProduceItems = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { data, error } =
        await producePreferencesService.getProduceItemsWithPreferences(
          user.id,
          activeCategory
        );

      if (error) {
        console.error('Failed to load produce items:', error);
        toast.error('Failed to load produce items');
        return;
      }

      if (data) {
        setProduceItems(data);
        // Set currently selected never send items
        const neverSendIds = data
          .filter((item) => item.is_never_send)
          .map((item) => item.id);
        setSelectedNeverSend(neverSendIds);

        // Check if user has existing preferences
        const hasPrefs = neverSendIds.length > 0;
        setHasExistingPreferences(hasPrefs);

        // If user has no preferences, start in editing mode
        // If user has preferences, start in view mode
        setIsEditing(!hasPrefs);
      }
    } catch (err) {
      console.error('Failed to load produce items:', err);
      toast.error('Failed to load produce items');
    } finally {
      setLoading(false);
    }
  };

  const handleNeverSendToggle = (itemId: string, checked: boolean) => {
    if (checked) {
      if (selectedNeverSend.length >= MAX_NEVER_SEND_ITEMS) {
        toast.error(
          `You can only select up to ${MAX_NEVER_SEND_ITEMS} "never send" items`
        );
        return;
      }
      setSelectedNeverSend([...selectedNeverSend, itemId]);
    } else {
      setSelectedNeverSend(selectedNeverSend.filter((id) => id !== itemId));
    }
  };

  const handleSave = async () => {
    if (!user) return;

    setSaving(true);
    try {
      const { error } = await producePreferencesService.updateNeverSendItems(
        user.id,
        selectedNeverSend
      );

      if (error) {
        console.error('Failed to save preferences:', error);
        toast.error('Failed to save preferences');
        return;
      }

      toast.success('Preferences saved successfully!');

      // Update state to reflect saved preferences
      setHasExistingPreferences(selectedNeverSend.length > 0);
      setIsEditing(false);

      // Reload to get updated data
      await loadProduceItems();
    } catch (err) {
      console.error('Failed to save preferences:', err);
      toast.error('Failed to save preferences');
    } finally {
      setSaving(false);
    }
  };

  const removeNeverSendItem = (itemId: string) => {
    setSelectedNeverSend(selectedNeverSend.filter((id) => id !== itemId));
  };

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    // Reset selections to saved preferences
    loadProduceItems();
  };

  const selectedItems = produceItems.filter((item) =>
    selectedNeverSend.includes(item.id)
  );

  if (!user) {
    return (
      <Card className={className}>
        <CardContent className='p-6'>
          <p className='text-center text-gray-500'>
            Please log in to manage your preferences.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className='flex justify-between items-start'>
          <div>
            <CardTitle>Produce Preferences</CardTitle>
            <p className='text-sm text-gray-600'>
              {isEditing ? (
                <>
                  Select up to {MAX_NEVER_SEND_ITEMS} items you never want to
                  receive in your box. We&apos;ll substitute them with something
                  of equal or greater value.
                </>
              ) : hasExistingPreferences ? (
                <>
                  Your current &quot;never send&quot; preferences. These items
                  will be automatically substituted in your boxes.
                </>
              ) : (
                <>
                  You haven&apos;t set any preferences yet. Click &quot;Set
                  Preferences&quot; to choose items you&apos;d like to avoid.
                </>
              )}
            </p>
          </div>
          {hasExistingPreferences && !isEditing && (
            <Button variant='outline' size='sm' onClick={handleEditClick}>
              Edit Preferences
            </Button>
          )}
          {!hasExistingPreferences && !isEditing && (
            <Button variant='default' size='sm' onClick={handleEditClick}>
              Set Preferences
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className='space-y-6'>
        {/* Current Never Send Items */}
        {selectedNeverSend.length > 0 && (
          <div>
            <h4 className='font-medium mb-3'>
              Your &quot;Never Send&quot; Items ({selectedNeverSend.length}/
              {MAX_NEVER_SEND_ITEMS})
            </h4>
            <div className='flex flex-wrap gap-2'>
              {selectedItems.map((item) => (
                <Badge
                  key={item.id}
                  variant={isEditing ? 'secondary' : 'default'}
                  className='flex items-center gap-1'
                >
                  {item.name}
                  {isEditing && (
                    <button
                      onClick={() => removeNeverSendItem(item.id)}
                      className='ml-1 hover:text-red-600'
                    >
                      <X className='h-3 w-3' />
                    </button>
                  )}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Category Tabs - Only show when editing */}
        {isEditing && (
          <div>
            <div className='flex flex-wrap gap-2 mb-4'>
              {Object.entries(PRODUCE_CATEGORIES).map(([key, label]) => (
                <Button
                  key={key}
                  variant={activeCategory === key ? 'default' : 'outline'}
                  size='sm'
                  onClick={() => setActiveCategory(key as ProduceCategory)}
                >
                  {label}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Produce Items - Only show when editing */}
        {isEditing && (
          <>
            {loading ? (
              <div className='flex items-center justify-center py-8'>
                <Loader2 className='h-6 w-6 animate-spin' />
                <span className='ml-2'>Loading produce items...</span>
              </div>
            ) : (
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3'>
                {produceItems.map((item) => {
                  const isSelected = selectedNeverSend.includes(item.id);
                  const canSelect =
                    selectedNeverSend.length < MAX_NEVER_SEND_ITEMS ||
                    isSelected;

                  return (
                    <div
                      key={item.id}
                      className={`flex items-center space-x-3 p-3 rounded-lg border ${
                        isSelected
                          ? 'bg-red-50 border-red-200'
                          : 'bg-gray-50 border-gray-200'
                      } ${!canSelect ? 'opacity-50' : ''}`}
                    >
                      <Checkbox
                        id={`item-${item.id}`}
                        checked={isSelected}
                        onCheckedChange={(checked) =>
                          handleNeverSendToggle(item.id, checked === true)
                        }
                        disabled={!canSelect}
                      />
                      <label
                        htmlFor={`item-${item.id}`}
                        className='text-sm font-medium cursor-pointer flex-1'
                      >
                        {item.name}
                      </label>
                    </div>
                  );
                })}
              </div>
            )}
          </>
        )}

        {/* Warning - Only show when editing */}
        {isEditing && selectedNeverSend.length >= MAX_NEVER_SEND_ITEMS && (
          <Alert>
            <AlertCircle className='h-4 w-4' />
            <AlertDescription>
              You&apos;ve reached the maximum of {MAX_NEVER_SEND_ITEMS}{' '}
              &quot;never send&quot; items. Remove an item to add a different
              one.
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons - Only show when editing */}
        {isEditing && (
          <div className='flex justify-end gap-2'>
            <Button variant='outline' onClick={handleCancelEdit}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={saving}>
              {saving && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
              Save Preferences
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
